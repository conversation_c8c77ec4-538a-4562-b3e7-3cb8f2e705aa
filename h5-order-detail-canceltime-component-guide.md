# H5订单详情取消时间字段 - 组件使用指南

## 1. 组件概述

### 1.1 功能描述
H5订单详情页面的取消时间字段组件，用于在已取消订单中显示具体的取消时间信息，提升订单信息的完整性和用户体验。

### 1.2 组件特性
- **条件显示**: 仅在订单状态为"已取消"时显示
- **时间格式化**: 自动格式化为用户友好的时间格式
- **视觉突出**: 使用特殊样式突出显示取消状态
- **响应式设计**: 适配不同移动设备屏幕尺寸

## 2. 使用方法

### 2.1 基础用法

#### 2.1.1 在Vue组件中使用
```vue
<template>
  <div class="order-detail">
    <!-- 订单基本信息 -->
    <div class="order-info">
      <!-- 下单时间 -->
      <div class="info-item">
        下单：{{ orderDetail.orderTimeStr }}
        <span class="creator">创建人：{{ orderDetail.creatorName }}</span>
      </div>
      
      <!-- 用车时间 -->
      <div class="info-item">
        用车：{{ orderDetail.startTimeStr }} 至 {{ orderDetail.endTimeStr }}, {{ orderDetail.durationStr }}
      </div>
      
      <!-- 取消时间字段 - 条件显示 -->
      <div v-if="shouldShowCancelTime" class="info-item cancel-time-item">
        <span class="cancel-time-text">
          {{ getCancelTimeDisplay() }}
        </span>
      </div>
      
      <!-- 订单号 -->
      <div class="info-item">
        订单号：{{ orderDetail.orderNo }}
        <button @click="copyOrderNo">复制</button>
      </div>
    </div>
  </div>
</template>

<script>
import { OrderDetailInfoModel } from '@/models/OrderDetailInfoModel'
import { Tempo } from '@/utils/tempo'

export default {
  name: 'OrderDetail',
  data() {
    return {
      orderDetail: new OrderDetailInfoModel()
    }
  },
  computed: {
    // 判断是否显示取消时间
    shouldShowCancelTime() {
      return this.orderDetail.status === 'CANCELLED' && 
             this.orderDetail.cancelTime
    }
  },
  methods: {
    // 获取取消时间显示文本
    getCancelTimeDisplay() {
      if (!this.orderDetail.cancelTime) return ''
      return `取消：${Tempo.formatCancelTime(this.orderDetail.cancelTime)}`
    },
    
    // 复制订单号
    copyOrderNo() {
      // 复制逻辑实现
    }
  }
}
</script>
```

#### 2.1.2 样式定义
```scss
<style lang="scss" scoped>
// 取消时间字段样式
.cancel-time-item {
  background: #fff7e6;
  border: 2px solid #ffa940;
  border-radius: 6px;
  padding: 8px;
  margin: 8px 0;
  position: relative;
  
  // 新功能标识
  &::before {
    content: "NEW";
    position: absolute;
    top: -8px;
    right: 8px;
    background: #ff4d4f;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
  }
}

.cancel-time-text {
  color: #d4380d;
  font-weight: 500;
  font-size: 14px;
}

// 基础信息项样式
.info-item {
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
  
  .creator {
    color: #666;
    margin-left: 8px;
  }
}
</style>
```

### 2.2 高级用法

#### 2.2.1 使用Vant组件库
```vue
<template>
  <van-cell-group>
    <!-- 基础订单信息 -->
    <van-cell>
      <div class="order-basic-info">
        <div class="info-row">
          <span>下单：{{ orderDetail.orderTimeStr }}</span>
          <span class="creator">创建人：{{ orderDetail.creatorName }}</span>
        </div>
        
        <div class="info-row">
          <span>用车：{{ orderDetail.startTimeStr }} 至 {{ orderDetail.endTimeStr }}</span>
        </div>
        
        <!-- 取消时间 - 使用Vant的Notice组件样式 -->
        <van-notice-bar
          v-if="shouldShowCancelTime"
          :text="getCancelTimeDisplay()"
          mode="closeable"
          color="#d4380d"
          background="#fff7e6"
          left-icon="warning-o"
        />
        
        <div class="info-row">
          <span>订单号：{{ orderDetail.orderNo }}</span>
          <van-button size="mini" @click="copyOrderNo">复制</van-button>
        </div>
      </div>
    </van-cell>
  </van-cell-group>
</template>

<script>
import { NoticeBar, Cell, CellGroup, Button } from 'vant'

export default {
  components: {
    [NoticeBar.name]: NoticeBar,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Button.name]: Button
  },
  // 其他配置同基础用法
}
</script>
```

#### 2.2.2 自定义样式主题
```scss
// 自定义主题变量
$cancel-theme: (
  bg-color: #fff7e6,
  border-color: #ffa940,
  text-color: #d4380d,
  badge-color: #ff4d4f
);

// 主题混入
@mixin cancel-time-theme($theme: $cancel-theme) {
  .cancel-time-item {
    background: map-get($theme, bg-color);
    border: 2px solid map-get($theme, border-color);
    
    &::before {
      background: map-get($theme, badge-color);
    }
  }
  
  .cancel-time-text {
    color: map-get($theme, text-color);
  }
}

// 应用主题
.order-detail {
  @include cancel-time-theme();
}

// 暗色主题
.order-detail.dark-theme {
  @include cancel-time-theme((
    bg-color: #2a1810,
    border-color: #d4380d,
    text-color: #ff7875,
    badge-color: #ff4d4f
  ));
}
```

## 3. 数据模型

### 3.1 OrderDetailInfoModel 扩展
```javascript
import { OrderBaseInfoModel } from './OrderBaseInfoModel'
import { Tempo } from '@/utils/tempo'

export class OrderDetailInfoModel extends OrderBaseInfoModel {
  constructor(data = {}) {
    super(data)
    
    // 取消时间相关字段
    this.cancelTime = data.cancelTime || null
    this.cancelTimeStr = this.formatCancelTime(data.cancelTime)
    this.cancelTimeRelative = this.getRelativeCancelTime(data.cancelTime)
  }
  
  // 格式化取消时间
  formatCancelTime(cancelTime) {
    return Tempo.formatCancelTime(cancelTime)
  }
  
  // 获取相对取消时间
  getRelativeCancelTime(cancelTime) {
    return Tempo.getRelativeCancelTime(cancelTime)
  }
  
  // 判断是否为已取消订单
  isCancelled() {
    return this.status === 'CANCELLED'
  }
  
  // 获取取消时间显示文本
  getCancelTimeDisplay() {
    if (!this.isCancelled() || !this.cancelTime) {
      return ''
    }
    return `取消：${this.cancelTimeStr}`
  }
}
```

### 3.2 API数据结构
```typescript
interface OrderDetailResponse {
  id: number
  orderNo: string
  status: 'CONFIRMED' | 'DISPATCHED' | 'PICKED_UP' | 'RETURNED' | 'CANCELLED'
  orderTime: string
  startTime: string
  endTime: string
  createTime: string
  updateTime: string
  
  // 新增字段
  cancelTime?: string | null
  
  // 其他字段
  vehicleInfo: VehicleInfo
  customerInfo: CustomerInfo
  priceInfo: PriceInfo
}
```

## 4. 工具函数

### 4.1 时间格式化工具
```javascript
// utils/tempo.js
export class Tempo {
  // 格式化取消时间
  static formatCancelTime(cancelTime) {
    if (!cancelTime) return ''
    
    try {
      return this.format(cancelTime, 'YYYY-MM-DD HH:mm')
    } catch (error) {
      console.error('Error formatting cancel time:', error)
      return ''
    }
  }
  
  // 获取相对时间
  static getRelativeCancelTime(cancelTime) {
    if (!cancelTime) return ''
    
    const now = new Date()
    const cancel = new Date(cancelTime)
    const diffMs = now.getTime() - cancel.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMinutes / 60)
    const diffDays = Math.floor(diffHours / 24)
    
    if (diffDays > 0) {
      return `${diffDays}天前取消`
    } else if (diffHours > 0) {
      return `${diffHours}小时前取消`
    } else if (diffMinutes > 0) {
      return `${diffMinutes}分钟前取消`
    } else {
      return '刚刚取消'
    }
  }
  
  // 基础时间格式化
  static format(time, pattern = 'YYYY-MM-DD HH:mm:ss') {
    if (!time) return ''
    
    const date = new Date(time)
    if (isNaN(date.getTime())) return ''
    
    return pattern
      .replace('YYYY', date.getFullYear())
      .replace('MM', String(date.getMonth() + 1).padStart(2, '0'))
      .replace('DD', String(date.getDate()).padStart(2, '0'))
      .replace('HH', String(date.getHours()).padStart(2, '0'))
      .replace('mm', String(date.getMinutes()).padStart(2, '0'))
      .replace('ss', String(date.getSeconds()).padStart(2, '0'))
  }
}
```

### 4.2 状态判断工具
```javascript
// utils/orderStatus.js
export const OrderStatus = {
  CONFIRMED: 'CONFIRMED',
  DISPATCHED: 'DISPATCHED', 
  PICKED_UP: 'PICKED_UP',
  RETURNED: 'RETURNED',
  CANCELLED: 'CANCELLED'
}

export const OrderStatusHelper = {
  // 判断是否为已取消订单
  isCancelled(status) {
    return status === OrderStatus.CANCELLED
  },
  
  // 获取状态显示文本
  getStatusText(status) {
    const statusMap = {
      [OrderStatus.CONFIRMED]: '已确认',
      [OrderStatus.DISPATCHED]: '已排车',
      [OrderStatus.PICKED_UP]: '已取车',
      [OrderStatus.RETURNED]: '已还车',
      [OrderStatus.CANCELLED]: '已取消'
    }
    return statusMap[status] || '未知状态'
  },
  
  // 获取状态颜色
  getStatusColor(status) {
    const colorMap = {
      [OrderStatus.CONFIRMED]: '#1890ff',
      [OrderStatus.DISPATCHED]: '#52c41a',
      [OrderStatus.PICKED_UP]: '#faad14',
      [OrderStatus.RETURNED]: '#52c41a',
      [OrderStatus.CANCELLED]: '#ff4d4f'
    }
    return colorMap[status] || '#666'
  }
}
```

## 5. 最佳实践

### 5.1 性能优化
```javascript
// 使用计算属性缓存结果
computed: {
  shouldShowCancelTime() {
    return this.orderDetail.status === 'CANCELLED' && 
           this.orderDetail.cancelTime
  },
  
  cancelTimeDisplay() {
    if (!this.shouldShowCancelTime) return ''
    return `取消：${this.orderDetail.cancelTimeStr}`
  }
},

// 避免在模板中进行复杂计算
// ❌ 不推荐
<div v-if="orderDetail.status === 'CANCELLED' && orderDetail.cancelTime">
  {{ `取消：${Tempo.formatCancelTime(orderDetail.cancelTime)}` }}
</div>

// ✅ 推荐
<div v-if="shouldShowCancelTime">
  {{ cancelTimeDisplay }}
</div>
```

### 5.2 错误处理
```javascript
methods: {
  getCancelTimeDisplay() {
    try {
      if (!this.orderDetail.cancelTime) return ''
      
      const formatted = Tempo.formatCancelTime(this.orderDetail.cancelTime)
      return formatted ? `取消：${formatted}` : ''
    } catch (error) {
      console.error('Error displaying cancel time:', error)
      return '取消时间格式错误'
    }
  }
}
```

### 5.3 可访问性
```vue
<template>
  <!-- 添加适当的ARIA标签 -->
  <div 
    v-if="shouldShowCancelTime" 
    class="cancel-time-item"
    role="alert"
    aria-label="订单取消时间信息"
  >
    <span class="cancel-time-text">
      {{ cancelTimeDisplay }}
    </span>
  </div>
</template>
```

## 6. 测试示例

### 6.1 单元测试
```javascript
import { mount } from '@vue/test-utils'
import OrderDetail from '@/views/order/OrderDetail.vue'

describe('OrderDetail Cancel Time', () => {
  test('显示已取消订单的取消时间', () => {
    const wrapper = mount(OrderDetail, {
      data() {
        return {
          orderDetail: {
            status: 'CANCELLED',
            cancelTime: '2025-05-19T11:45:00Z'
          }
        }
      }
    })
    
    expect(wrapper.find('.cancel-time-item').exists()).toBe(true)
    expect(wrapper.find('.cancel-time-text').text()).toContain('取消：')
  })
  
  test('不显示非取消订单的取消时间', () => {
    const wrapper = mount(OrderDetail, {
      data() {
        return {
          orderDetail: {
            status: 'CONFIRMED',
            cancelTime: null
          }
        }
      }
    })
    
    expect(wrapper.find('.cancel-time-item').exists()).toBe(false)
  })
})
```

### 6.2 集成测试
```javascript
describe('OrderDetail Integration', () => {
  test('从API获取数据后正确显示取消时间', async () => {
    const mockApiResponse = {
      status: 'CANCELLED',
      cancelTime: '2025-05-19T11:45:00Z'
    }
    
    // 模拟API调用
    jest.spyOn(api, 'getOrderDetail').mockResolvedValue(mockApiResponse)
    
    const wrapper = mount(OrderDetail)
    await wrapper.vm.loadOrderDetail()
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('.cancel-time-item').exists()).toBe(true)
  })
})
```

## 7. 常见问题

### 7.1 时间格式问题
**问题**: 取消时间显示格式不正确
**解决**: 确保使用正确的时间格式化工具，处理时区转换

```javascript
// 正确处理时区
formatCancelTime(cancelTime) {
  if (!cancelTime) return ''
  
  // 确保时间字符串格式正确
  const date = new Date(cancelTime)
  if (isNaN(date.getTime())) {
    console.warn('Invalid cancel time format:', cancelTime)
    return ''
  }
  
  // 使用本地时区格式化
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
```

### 7.2 样式兼容性问题
**问题**: 在某些设备上样式显示异常
**解决**: 添加浏览器前缀，使用兼容性更好的CSS属性

```scss
.cancel-time-item {
  // 添加浏览器前缀
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  
  // 使用flexbox替代复杂定位
  display: flex;
  align-items: center;
  justify-content: space-between;
}
```

### 7.3 数据为空的处理
**问题**: 取消时间为空或null时的处理
**解决**: 添加完善的空值检查

```javascript
computed: {
  shouldShowCancelTime() {
    return this.orderDetail.status === 'CANCELLED' && 
           this.orderDetail.cancelTime &&
           this.orderDetail.cancelTime !== 'null' &&
           this.orderDetail.cancelTime !== ''
  }
}
```
