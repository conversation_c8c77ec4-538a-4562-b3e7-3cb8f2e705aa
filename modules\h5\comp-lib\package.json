{"name": "qinglu-vant", "version": "1.0.0", "description": "擎路H5组件库 - 基于Vue3 + Vant的移动端组件库", "type": "module", "private": true, "scripts": {"dev": "vite --host", "build": "npm run clean && npm run build:lib", "build:lib": "vite build --mode lib", "build:demo": "vite build", "clean": "<PERSON><PERSON><PERSON> dist", "preview": "vite preview", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "lint": "eslint --ext .js,.ts,.vue ./src", "lint:fix": "eslint --ext .js,.ts,.vue ./src --fix", "test": "vitest", "test:ui": "vitest --ui", "coverage": "vitest run --coverage"}, "keywords": ["vue", "vue3", "vant", "mobile", "components", "ui", "qinglu"], "author": "supan", "license": "MIT", "dependencies": {"@lihzsky/data-x": "^0.0.5", "@lihzsky/type-checker": "^0.0.1", "@tailwindcss/postcss": "^4.1.11", "@vant/use": "^1.4.3", "animate.css": "^4.1.1", "axios": "^1.2.0", "compressorjs": "^1.1.1", "dayjs": "^1.11.10", "file-type": "^18.0.0", "html2canvas": "^1.4.1", "lodash-es": "^4.17.21", "moment": "^2.29.4", "nanoid": "^4.0.0", "nanoid-dictionary": "^4.3.0", "qrcode.vue": "^3.6.0", "signature_pad": "^4.1.5", "vant": "^3.6.8", "vue": "^3.2.0", "vue-arc-counter": "^3.1.0", "vue-clipboard3": "^2.0.0", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^3.2.0", "@vitest/coverage-v8": "^3.0.7", "@vitest/ui": "^3.0.7", "@vue/test-utils": "^2.0.0", "autoprefixer": "^10.4.13", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "jsdom": "^21.1.0", "less": "^4.1.3", "mockjs": "^1.1.0", "postcss": "^8.4.19", "postcss-pxtorem": "^6.0.0", "prettier": "^2.8.0", "rimraf": "^5.0.0", "tailwindcss": "^3.4.17", "typescript": "^5.0.0", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^3.2.4", "vite-plugin-mock": "^2.9.6", "vitepress": "^1.0.0", "vitest": "^3.0.7", "vue-tsc": "^1.8.0"}, "peerDependencies": {"vue": "^3.2.0"}}