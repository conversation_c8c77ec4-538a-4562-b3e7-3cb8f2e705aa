# H5订单详情取消时间字段 - 接口规范文档 v1.0.5

## 文档信息

- **项目**: 擎路 SaaS 管理系统
- **功能**: H5订单详情页面新增取消时间字段
- **版本**: v1.0.5
- **平台**: H5 移动端
- **创建时间**: 2025-07-30
- **文档类型**: 接口规范文档

## 接口概述

本次功能开发复用现有的订单详情接口，无需新增接口。主要变更为在现有接口响应数据中新增 `cancelTime` 字段，并在前端进行相应的数据处理和显示。

## 接口详情

### 1. 订单详情接口

#### 1.1 基本信息

- **接口名称**: 获取订单详情
- **接口路径**: `/api/order/v1/detail`
- **请求方法**: POST
- **接口说明**: 获取指定订单的详细信息，包括订单基本信息、车辆信息、费用信息等

#### 1.2 请求参数

**请求头 (Headers)**

```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer {access_token}"
}
```

**请求体 (Body)**

```json
{
  "orderId": "3792163"
}
```

**参数说明**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | String | 是 | 订单ID |

#### 1.3 响应参数

**响应格式**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "orderInfoVo": {
      "orderId": "3792163",
      "orderStatus": 8,
      "orderStatusStr": "已取消",
      "orderTime": "2025-07-07T12:00:00Z",
      "orderTimeStr": "2025-07-07 12:00",
      "pickupDate": "2025-07-07T15:00:00Z",
      "pickupDateStr": "2025-07-07",
      "pickupDateTimeStr": "2025-07-07 15:00",
      "returnDate": "2025-07-11T15:00:00Z",
      "returnDateStr": "2025-07-11",
      "returnDateTimeStr": "2025-07-11 15:00",
      "remainTimeStr": "4天",
      "cancelTime": "2025-07-07T16:30:00Z",
      "cancelTimeStr": "07-07 16:30",
      "orderMemberVo": {
        "customerName": "张三",
        "mobile": "138****8888"
      }
    },
    "orderDepositVo": {
      // 押金信息...
    },
    "orderPickReturnVo": {
      // 取还车信息...
    }
    // ... 其他现有数据结构
  }
}
```

**新增字段说明**

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| cancelTime | String | 订单取消时间(ISO 8601格式) | "2025-07-07T16:30:00Z" |
| cancelTimeStr | String | 格式化后的取消时间(前端处理) | "07-07 16:30" |

**字段约束**

- `cancelTime`: 仅在订单状态为已取消(orderStatus=8)时有值，其他状态为null
- `cancelTimeStr`: 由前端数据模型根据cancelTime和orderStatus计算得出
- 时间格式: 后端返回UTC时间，前端转换为本地时间显示

### 2. 数据处理逻辑

#### 2.1 后端处理逻辑

```java
// 伪代码示例
public OrderDetailResponse getOrderDetail(String orderId) {
    Order order = orderService.getById(orderId);
    OrderDetailResponse response = new OrderDetailResponse();
    
    // 基本信息映射
    response.setOrderId(order.getOrderId());
    response.setOrderStatus(order.getOrderStatus());
    response.setOrderTime(order.getOrderTime());
    
    // 取消时间处理
    if (order.getOrderStatus() == 8 && order.getCancelTime() != null) {
        response.setCancelTime(order.getCancelTime());
    } else {
        response.setCancelTime(null);
    }
    
    return response;
}
```

#### 2.2 前端处理逻辑

```javascript
// OrderDetailInfoModel.js 中的处理
cancelTimeStr = {
  type: String,
  field: (data) => {
    if (data.cancelTime && data.orderStatus === 8) {
      return Tempo.format(data.cancelTime, 'MM-dd HH:mm')
    }
    return null
  }
}
```

### 3. 错误处理

#### 3.1 常见错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 200 | success | 请求成功 |
| 400 | 参数错误 | 请求参数格式错误 |
| 401 | 未授权 | 访问令牌无效或过期 |
| 404 | 订单不存在 | 指定的订单ID不存在 |
| 500 | 服务器内部错误 | 服务器处理异常 |

#### 3.2 错误响应格式

```json
{
  "code": 404,
  "message": "订单不存在",
  "data": null
}
```

#### 3.3 前端错误处理

```javascript
// API调用错误处理
try {
  const response = await orderAPI.getOrderDetail(orderId)
  if (response.code === 200) {
    // 成功处理
    this.orderDetail = response.data
  } else {
    // 业务错误处理
    this.$toast(response.message || '获取订单详情失败')
  }
} catch (error) {
  // 网络错误处理
  console.error('获取订单详情异常:', error)
  this.$toast('网络异常，请稍后重试')
}
```

## 数据兼容性

### 1. 向后兼容

#### 1.1 历史数据处理

对于历史订单数据，可能存在以下情况：

```json
// 情况1: 有取消时间的新数据
{
  "orderStatus": 8,
  "cancelTime": "2025-07-07T16:30:00Z"
}

// 情况2: 无取消时间的历史数据
{
  "orderStatus": 8,
  "cancelTime": null
}

// 情况3: 非取消状态订单
{
  "orderStatus": 5,
  "cancelTime": null
}
```

#### 1.2 前端兼容处理

```javascript
// 兼容性处理逻辑
const getCancelTimeDisplay = (orderData) => {
  // 检查数据完整性
  if (!orderData || typeof orderData.orderStatus !== 'number') {
    return null
  }
  
  // 仅处理已取消状态的订单
  if (orderData.orderStatus !== 8) {
    return null
  }
  
  // 检查取消时间是否存在
  if (!orderData.cancelTime) {
    return null
  }
  
  // 格式化显示
  try {
    return Tempo.format(orderData.cancelTime, 'MM-dd HH:mm')
  } catch (error) {
    console.warn('取消时间格式化失败:', error)
    return null
  }
}
```

### 2. 版本兼容

#### 2.1 前端版本兼容

- **新版本前端**: 支持显示取消时间字段
- **旧版本前端**: 忽略新增字段，不影响现有功能
- **渐进升级**: 支持前后端独立升级

#### 2.2 后端版本兼容

- **新版本后端**: 返回包含cancelTime的完整数据
- **旧版本后端**: 不返回cancelTime字段，前端优雅降级
- **数据迁移**: 历史数据的cancelTime字段可为空

## 接口测试

### 1. 测试用例

#### 1.1 正常场景测试

**测试用例1: 已取消订单且有取消时间**

```bash
# 请求
curl -X POST "https://api.example.com/api/order/v1/detail" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"orderId": "3792163"}'

# 预期响应
{
  "code": 200,
  "data": {
    "orderInfoVo": {
      "orderId": "3792163",
      "orderStatus": 8,
      "cancelTime": "2025-07-07T16:30:00Z"
    }
  }
}
```

**测试用例2: 已取消订单但无取消时间**

```bash
# 预期响应
{
  "code": 200,
  "data": {
    "orderInfoVo": {
      "orderId": "3792164",
      "orderStatus": 8,
      "cancelTime": null
    }
  }
}
```

**测试用例3: 非取消状态订单**

```bash
# 预期响应
{
  "code": 200,
  "data": {
    "orderInfoVo": {
      "orderId": "3792165",
      "orderStatus": 5,
      "cancelTime": null
    }
  }
}
```

#### 1.2 异常场景测试

**测试用例4: 订单不存在**

```bash
# 请求
curl -X POST "https://api.example.com/api/order/v1/detail" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"orderId": "nonexistent"}'

# 预期响应
{
  "code": 404,
  "message": "订单不存在",
  "data": null
}
```

### 2. 性能测试

#### 2.1 响应时间要求

- **正常响应时间**: < 500ms
- **99%响应时间**: < 1000ms
- **并发处理能力**: 支持1000 QPS

#### 2.2 测试指标

| 指标 | 目标值 | 测试方法 |
|------|--------|----------|
| 平均响应时间 | < 300ms | 压力测试 |
| 成功率 | > 99.9% | 稳定性测试 |
| 并发处理 | 1000 QPS | 负载测试 |

## 部署说明

### 1. 部署顺序

1. **后端部署**: 确保cancelTime字段正确返回
2. **前端部署**: 更新数据模型和UI组件
3. **验证测试**: 确认功能正常工作

### 2. 回滚方案

- **前端回滚**: 移除取消时间显示逻辑
- **后端回滚**: 移除cancelTime字段返回
- **数据回滚**: 不影响现有数据结构

### 3. 监控指标

- **接口调用量**: 监控订单详情接口调用频次
- **错误率**: 监控接口错误率变化
- **响应时间**: 监控接口响应时间变化

---

**备注**: 本接口规范文档基于擎路SaaS系统现有接口架构，确保新增字段与现有系统的完美集成。开发和测试时请严格按照规范执行。
