{"token": "b3fa42b5e20b45b3adf7d2e780ad2c47", "userInfo": {"id": 1062, "name": "企鹅123", "loginName": "sjz2025", "mobile": "", "email": "<EMAIL>", "status": 1, "childAccount": 1, "parentId": 290, "isAdmin": 1, "merchantId": 44, "merchantName": null, "isBinded": null, "isSubscribed": null, "deleted": 0, "createTime": *************, "createUserId": 290, "opTime": *************, "opUserId": 1006, "roleId": 2, "roleName": "管理员", "storeIds": [0], "menuAutos": ["order", "procure", "procure:service", "car:violation", "Finance", "order:elecContract", "stock", "price", "report", "store", "car", "merchant", "task", "bill", "market", "channel", "user", "etc", "user:account", "store:table", "car:model", "stock:stock", "price:setting", "channel:manage", "order:table", "task:detail", "report:data", "merchant:flair", "market:manage", "bill:manage", "Finance:incomeDetail", "etc:car", "user:role", "rule", "car:vehicle", "price:rent", "order:detail", "task:cost", "Finance:shortReconcilia", "etc:publish", "car:ticket", "price:added", "user:push", "task:driver", "longOrder:manage", "etc:order", "price:addedprice", "car:breakRules", "longOrder:list", "etc:page", "car:device", "price:insurance", "longOrder:detail", "price:insuranceprice", "price:credit", "user:account:list", "user:role:list", "store:list", "rule:list", "car:model:list", "car:vehicle:list", "car:ticket:list", "price:setting:list", "price:rent:add", "price:added:list", "price:addedprice:list", "price:insurance:list", "price:insuranceprice:list", "price:credit:list", "channel:manage:list", "order:table:list", "order:detail:list", "task:detail:list", "task:cost:list", "order:detail:list:query", "order:table:list:query", "user:push:list", "car:breakRules:list", "task:driver:list", "report:data:list", "merchant:flair:search", "market:manage:list", "bill:manage:list", "longOrder:manage:list", "longOrder:list:add", "longOrder:detail:cancel", "stock:shopVehicle", "order:contract:view", "etc:car:list", "etc:publish:list", "etc:order:list", "user:account:add", "user:role:add", "store:add", "rule:edit", "car:model:add", "car:vehicle:add:edit", "car:ticket:add", "price:setting:detail:edit", "price:rent:edit", "price:added:add", "price:addedprice:edit", "price:insurance:add", "price:insuranceprice:edit", "price:credit:open", "channel:manage:auth", "order:table:add", "order:detail:edit", "task:detail:add", "task:cost:manage", "user:push:edit", "car:breakRules:add", "task:driver:add", "report:data:reset", "market:manage:reset", "bill:manage:recharge", "longOrder:list:export", "longOrder:detail:addDamage", "stock:all", "order:contract:edit", "etc:withdrawal", "etc:car:export", "user:account:disabled", "user:role:edit", "store:status", "rule:disabled", "car:model:edit", "car:vehicle:export", "car:ticket:export", "price:setting:sell:suspend", "price:added:disabled", "price:insurance:disabled", "price:credit:close", "channel:manage:apply:plan", "order:table:export", "order:detail:cancel:order", "task:detail:export", "task:cost:view:evidence", "car:breakRules:handle", "task:driver:export", "report:data:export", "market:manage:add", "bill:manage:withdrawal", "longOrder:list:list", "longOrder:detail:add<PERSON><PERSON><PERSON>le", "user:account:detail", "store:detail", "rule:add", "car:model:remove", "price:setting:manage:car", "price:added:edit", "price:insurance:edit", "price:credit:batch:open", "channel:manage:apply:auth", "order:detail:continuous:rent", "task:detail:view:detail", "car:ticket:handle", "car:breakRules:import", "bill:manage:invoicing", "longOrder:list:payment", "longOrder:detail:paymentPlan", "order:table:platDriver", "car:violation:ruleSetting", "user:account:edit", "store:edit", "car:model:export", "price:added:remove", "price:insurance:remove", "price:credit:batch:close", "order:detail:add:loss", "car:ticket:cancel", "car:breakRules:export", "bill:manage:invoicingInfo", "longOrder:list:addWork", "longOrder:detail:edit<PERSON><PERSON><PERSON>le", "order:table:forceUpdateVehicle", "store:servescope", "order:detail:add:breakrule", "bill:manage:invoicingBatch", "longOrder:list:platoon", "longOrder:detail:editR<PERSON>", "order:contract", "order:table:updVehicle", "order:detail:continuous:rent:can", "longOrder:list:paymentPlan", "order:detail:loss:refund", "order:detail:breakrule:refund", "order:detail:changeExpenses", "order:detail:updVehicle", "merchant:flair:edit"], "eventType": null}}