# H5订单详情取消时间字段 - 组件对照表 v1.0.5

## 文档信息

- **项目**: 擎路 SaaS 管理系统
- **功能**: H5订单详情页面新增取消时间字段
- **版本**: v1.0.5
- **平台**: H5 移动端
- **创建时间**: 2025-07-30
- **文档类型**: 组件对照表

## 组件变更对照

### 1. 数据模型组件

#### 1.1 OrderDetailInfoModel

**文件路径**: `modules/h5/comp-lib/src/services/models/OrderDetailModels/OrderDetailInfoModel.js`

| 变更类型 | 变更内容 | 变更前 | 变更后 |
|---------|----------|--------|--------|
| 新增字段 | cancelTimeStr | 无 | 新增取消时间格式化字段 |

**具体变更**:

```javascript
// 新增内容
cancelTimeStr = {
  type: String,
  field: (data) => {
    if (data.cancelTime && data.orderStatus === 8) {
      return Tempo.format(data.cancelTime, 'MM-dd HH:mm')
    }
    return null
  }
}
```

#### 1.2 OrderBaseInfoModel

**文件路径**: `modules/h5/comp-lib/src/services/models/OrderDetailModels/OrderBaseInfoModel.js`

| 变更类型 | 变更内容 | 变更前 | 变更后 |
|---------|----------|--------|--------|
| 新增字段 | cancelTimeStr | 无 | 新增取消时间格式化字段 |
| 新增字段 | cancelDateStr | 无 | 新增取消日期格式化字段(备用) |

**具体变更**:

```javascript
// 新增内容
cancelTimeStr = {
  type: String,
  field: (data) => {
    if (data.cancelTime && data.orderStatus === 8) {
      return Tempo.format(data.cancelTime, 'MM-dd HH:mm')
    }
    return ''
  }
}

cancelDateStr = {
  type: String,
  field: (data) => {
    if (data.cancelTime && data.orderStatus === 8) {
      return Tempo.format(data.cancelTime)
    }
    return ''
  }
}
```

### 2. UI组件变更

#### 2.1 订单详情页面组件

**文件路径**: `modules/h5/订单详情页面组件.vue` (具体路径待确认)

| 变更类型 | 变更内容 | 变更前 | 变更后 |
|---------|----------|--------|--------|
| 模板新增 | 取消时间显示行 | 无 | 新增条件显示的取消时间信息行 |
| 样式新增 | 取消时间样式 | 无 | 新增红色标签和对应的样式定义 |
| 逻辑新增 | 显示控制逻辑 | 无 | 新增取消时间的显示判断逻辑 |

**模板变更**:

```vue
<!-- 新增内容 -->
<div 
  v-if="orderDetail.orderInfoVo.cancelTimeStr" 
  class="info-row cancel-time-row"
>
  <span class="info-label cancel-label">取消:</span>
  <span class="info-value cancel-value">
    {{ orderDetail.orderInfoVo.cancelTimeStr }}
  </span>
</div>
```

**样式变更**:

```less
/* 新增样式 */
.cancel-time-row {
  .cancel-label {
    color: #ff4d4f;
    font-weight: 500;
  }
  
  .cancel-value {
    color: #666666;
  }
}
```

### 3. 接口相关组件

#### 3.1 订单详情API

**文件路径**: `modules/h5/comp-lib/src/services/apis/order.js`

| 变更类型 | 变更内容 | 变更前 | 变更后 |
|---------|----------|--------|--------|
| 接口扩展 | 响应数据结构 | 无cancelTime字段 | 新增cancelTime字段支持 |

**接口变更**:

```javascript
// getOrderDetail 接口响应数据新增字段
{
  orderInfoVo: {
    // 现有字段...
    cancelTime: "2025-07-07T16:30:00Z", // 新增
    cancelTimeStr: "07-07 16:30"        // 前端计算
  }
}
```

### 4. 工具类组件

#### 4.1 时间格式化工具

**文件路径**: `modules/h5/comp-lib/src/utils/index.js`

| 变更类型 | 变更内容 | 变更前 | 变更后 |
|---------|----------|--------|--------|
| 使用扩展 | Tempo.format使用 | 现有用法 | 新增MM-dd HH:mm格式的使用 |

**使用示例**:

```javascript
// 新增使用方式
Tempo.format(data.cancelTime, 'MM-dd HH:mm')
```

### 5. 常量定义组件

#### 5.1 订单状态常量

**文件路径**: `modules/h5/comp-lib/src/constants/index.js`

| 变更类型 | 变更内容 | 变更前 | 变更后 |
|---------|----------|--------|--------|
| 确认使用 | 订单状态8 | 已存在 | 确认用于取消状态判断 |

**相关代码**:

```javascript
// 确认使用的状态值
export function getOrderStatusStr(value) {
  switch (value) {
    case 8: return '已取消'  // 用于判断显示取消时间
    // ... 其他状态
  }
}
```

## 文件变更清单

### 1. 需要修改的文件

| 序号 | 文件路径 | 变更类型 | 变更说明 |
|------|----------|----------|----------|
| 1 | `modules/h5/comp-lib/src/services/models/OrderDetailModels/OrderDetailInfoModel.js` | 修改 | 新增cancelTimeStr字段 |
| 2 | `modules/h5/comp-lib/src/services/models/OrderDetailModels/OrderBaseInfoModel.js` | 修改 | 新增cancelTimeStr和cancelDateStr字段 |
| 3 | `modules/h5/订单详情页面组件.vue` | 修改 | 新增取消时间显示UI |
| 4 | `modules/h5/订单详情页面样式.less` | 修改 | 新增取消时间相关样式 |

### 2. 不需要修改的文件

| 序号 | 文件路径 | 说明 |
|------|----------|------|
| 1 | `modules/h5/comp-lib/src/services/apis/order.js` | 接口调用逻辑无需变更 |
| 2 | `modules/h5/comp-lib/src/utils/index.js` | 时间工具类无需变更 |
| 3 | `modules/h5/comp-lib/src/constants/index.js` | 常量定义无需变更 |

### 3. 新增的文件

| 序号 | 文件路径 | 文件类型 | 说明 |
|------|----------|----------|------|
| 1 | `prd/v1.0.5/h5/H5订单详情取消时间字段-PRD-v1.0.5.md` | 文档 | 产品需求文档 |
| 2 | `prd/v1.0.5/h5/H5订单详情取消时间字段-数据模型变更说明-v1.0.5.md` | 文档 | 数据模型变更说明 |
| 3 | `prd/v1.0.5/h5/H5订单详情取消时间字段-UI组件变更方案-v1.0.5.md` | 文档 | UI组件变更方案 |
| 4 | `prd/v1.0.5/h5/H5订单详情取消时间字段-接口规范文档-v1.0.5.md` | 文档 | 接口规范文档 |
| 5 | `prd/v1.0.5/h5/H5订单详情取消时间字段-组件对照表-v1.0.5.md` | 文档 | 组件对照表 |

## 依赖关系图

```mermaid
graph TD
    A[后端API] --> B[OrderDetailInfoModel]
    A --> C[OrderBaseInfoModel]
    B --> D[订单详情页面组件]
    C --> D
    E[Tempo工具类] --> B
    E --> C
    F[订单状态常量] --> B
    F --> C
    D --> G[用户界面显示]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#f3e5f5
    style D fill:#e8f5e8
    style G fill:#fff3e0
```

## 测试对照表

### 1. 单元测试

| 组件 | 测试用例 | 预期结果 |
|------|----------|----------|
| OrderDetailInfoModel | 有取消时间的已取消订单 | 返回格式化的取消时间 |
| OrderDetailInfoModel | 无取消时间的已取消订单 | 返回null |
| OrderDetailInfoModel | 非取消状态订单 | 返回null |
| OrderBaseInfoModel | 有取消时间的已取消订单 | 返回格式化的取消时间 |
| OrderBaseInfoModel | 无取消时间的已取消订单 | 返回空字符串 |

### 2. 集成测试

| 测试场景 | 测试步骤 | 预期结果 |
|----------|----------|----------|
| 已取消订单详情页面 | 1. 进入订单详情页面<br>2. 查看订单信息区域 | 显示取消时间信息 |
| 进行中订单详情页面 | 1. 进入订单详情页面<br>2. 查看订单信息区域 | 不显示取消时间信息 |
| 历史订单详情页面 | 1. 进入历史已取消订单详情<br>2. 查看订单信息区域 | 根据数据情况显示或不显示 |

### 3. UI测试

| 测试项目 | 测试内容 | 预期结果 |
|----------|----------|----------|
| 样式一致性 | 取消时间行样式 | 与其他信息行样式保持一致 |
| 颜色规范 | 取消标签颜色 | 使用红色(#ff4d4f) |
| 响应式适配 | 不同屏幕尺寸 | 正常显示和布局 |
| 字体规范 | 字体大小和粗细 | 符合设计规范 |

## 版本兼容性对照

### 1. 前端版本兼容

| 前端版本 | 后端版本 | 兼容性 | 说明 |
|----------|----------|--------|------|
| v1.0.5+ | v1.0.5+ | ✅ 完全兼容 | 支持取消时间显示 |
| v1.0.5+ | v1.0.4- | ✅ 向下兼容 | 不显示取消时间，不影响其他功能 |
| v1.0.4- | v1.0.5+ | ✅ 向上兼容 | 忽略新增字段，正常工作 |
| v1.0.4- | v1.0.4- | ✅ 完全兼容 | 原有功能正常 |

### 2. 数据兼容性

| 数据情况 | 处理方式 | 显示结果 |
|----------|----------|----------|
| 新订单取消(有cancelTime) | 正常处理 | 显示取消时间 |
| 历史订单取消(无cancelTime) | 优雅降级 | 不显示取消时间 |
| 非取消状态订单 | 条件过滤 | 不显示取消时间 |
| 数据异常 | 异常处理 | 不显示取消时间 |

---

**备注**: 本组件对照表详细记录了H5订单详情取消时间字段功能的所有组件变更，为开发、测试和维护提供准确的参考依据。
