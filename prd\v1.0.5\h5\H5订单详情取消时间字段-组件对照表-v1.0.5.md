# H5订单详情取消时间字段 - 组件对照表 v1.0.5

## 📋 文档信息

| 项目 | 内容 |
|------|------|
| **文档标题** | H5订单详情取消时间字段 - 组件对照表 |
| **版本号** | v1.0.5 |
| **平台** | H5移动端 |
| **创建日期** | 2025-07-30 |
| **文档类型** | 组件对照表 |

## 🎯 组件概述

### 功能描述
在H5订单详情页面新增取消时间字段显示组件，为已取消订单提供取消时间信息展示。

### 组件层级
```
OrderDetail.vue (主页面)
├── van-cell-group (Vant组件)
│   └── van-cell (Vant组件)
│       └── order-info (自定义容器)
│           ├── info-item (基础信息项)
│           ├── cancel-time-item (🆕 新增取消时间组件)
│           └── info-item (其他信息项)
```

## 🧩 组件对照表

### 1. 主要组件映射

| 功能区域 | 组件名称 | 组件来源 | 文件路径 | 说明 |
|----------|----------|----------|----------|------|
| 页面容器 | `OrderDetail` | 自定义组件 | `views/order/OrderDetail.vue` | 订单详情主页面 |
| 单元格组 | `van-cell-group` | Vant 3 | `vant/es/cell` | 信息分组容器 |
| 单元格 | `van-cell` | Vant 3 | `vant/es/cell` | 基础信息单元 |
| 按钮组件 | `van-button` | Vant 3 | `vant/es/button` | 复制按钮等 |
| 🆕 取消时间 | `cancel-time-item` | 自定义样式类 | 内联样式 | 新增取消时间显示 |

### 2. 样式组件对照

| 样式类名 | 组件类型 | 样式来源 | 作用范围 | 说明 |
|----------|----------|----------|----------|------|
| `.order-detail` | 容器类 | 自定义 | 整个页面 | 主页面容器样式 |
| `.order-info` | 容器类 | 自定义 | 订单信息区域 | 订单基本信息容器 |
| `.info-item` | 基础类 | 自定义 | 信息项 | 通用信息项样式 |
| 🆕 `.cancel-time-item` | 特殊类 | 自定义 | 取消时间 | 新增取消时间容器 |
| 🆕 `.cancel-time-text` | 文本类 | 自定义 | 取消时间文本 | 新增取消时间文字 |
| `.creator` | 文本类 | 自定义 | 创建人信息 | 创建人文字样式 |

### 3. 数据模型对照

| 数据字段 | 模型类 | 文件路径 | 数据类型 | 说明 |
|----------|--------|----------|----------|------|
| `orderDetail` | `OrderDetailInfoModel` | `models/OrderDetailInfoModel.js` | Object | 订单详情数据模型 |
| `status` | `OrderEntity` | `entities/OrderEntity.js` | String | 订单状态 |
| `orderTimeStr` | `OrderDetailInfoModel` | `models/OrderDetailInfoModel.js` | String | 格式化下单时间 |
| 🆕 `cancelTime` | `OrderEntity` | `entities/OrderEntity.js` | String | 原始取消时间 |
| 🆕 `cancelTimeStr` | `OrderDetailInfoModel` | `models/OrderDetailInfoModel.js` | String | 格式化取消时间 |

### 4. 工具函数对照

| 函数名称 | 工具类 | 文件路径 | 返回类型 | 说明 |
|----------|--------|----------|----------|------|
| `format()` | `Tempo` | `utils/tempo.js` | String | 基础时间格式化 |
| 🆕 `formatCancelTime()` | `Tempo` | `utils/tempo.js` | String | 取消时间专用格式化 |
| 🆕 `getRelativeCancelTime()` | `Tempo` | `utils/tempo.js` | String | 相对时间描述 |
| 🆕 `isValidTime()` | `Tempo` | `utils/tempo.js` | Boolean | 时间格式验证 |

## 🎨 UI组件详细对照

### 1. Vant组件使用

#### 1.1 van-cell-group
```vue
<van-cell-group>
  <!-- 订单信息内容 -->
</van-cell-group>
```

| 属性 | 值 | 说明 |
|------|----|----- |
| 默认插槽 | 订单信息内容 | 包含所有订单基本信息 |

#### 1.2 van-cell
```vue
<van-cell>
  <div class="order-info">
    <!-- 具体信息项 -->
  </div>
</van-cell>
```

| 属性 | 值 | 说明 |
|------|----|----- |
| 默认插槽 | 订单信息容器 | 自定义订单信息布局 |

#### 1.3 van-button
```vue
<van-button size="mini" @click="copyOrderNo">复制</van-button>
```

| 属性 | 值 | 说明 |
|------|----|----- |
| `size` | "mini" | 小尺寸按钮 |
| `@click` | `copyOrderNo` | 点击复制订单号 |

### 2. 自定义组件结构

#### 2.1 订单信息容器
```vue
<div class="order-info">
  <div class="info-item">下单时间</div>
  <div class="info-item">用车时间</div>
  <div class="info-item cancel-time-item">🆕 取消时间</div>
  <div class="info-item">订单号</div>
</div>
```

#### 2.2 取消时间组件
```vue
<div v-if="shouldShowCancelTime" class="info-item cancel-time-item">
  <span class="cancel-time-text">
    {{ getCancelTimeDisplay() }}
  </span>
</div>
```

| 元素 | 类名 | 说明 |
|------|------|------|
| 容器div | `info-item cancel-time-item` | 取消时间容器，继承基础样式并添加特殊样式 |
| 文本span | `cancel-time-text` | 取消时间文字，应用特殊颜色和字重 |

## 🎭 样式对照表

### 1. 基础样式类

#### 1.1 .order-detail
```scss
.order-detail {
  // 主页面容器样式
  background: #f5f5f5;
  min-height: 100vh;
}
```

| 属性 | 值 | 说明 |
|------|----|----- |
| `background` | #f5f5f5 | 页面背景色 |
| `min-height` | 100vh | 最小高度为视口高度 |

#### 1.2 .order-info
```scss
.order-info {
  // 订单信息容器样式
  padding: 16px;
}
```

| 属性 | 值 | 说明 |
|------|----|----- |
| `padding` | 16px | 内边距 |

#### 1.3 .info-item
```scss
.info-item {
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
}
```

| 属性 | 值 | 说明 |
|------|----|----- |
| `margin-bottom` | 8px | 下边距 |
| `font-size` | 14px | 字体大小 |
| `color` | #333 | 文字颜色 |

### 2. 新增样式类

#### 2.1 .cancel-time-item (🆕 新增)
```scss
.cancel-time-item {
  background: #fff7e6;
  border: 2px solid #ffa940;
  border-radius: 6px;
  padding: 8px;
  margin: 8px 0;
  position: relative;
  
  &::before {
    content: "NEW";
    position: absolute;
    top: -8px;
    right: 8px;
    background: #ff4d4f;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    z-index: 1;
  }
}
```

| 属性 | 值 | 说明 |
|------|----|----- |
| `background` | #fff7e6 | 浅橙色背景 |
| `border` | 2px solid #ffa940 | 橙色边框 |
| `border-radius` | 6px | 圆角 |
| `padding` | 8px | 内边距 |
| `margin` | 8px 0 | 上下外边距 |
| `position` | relative | 相对定位（为伪元素定位） |

#### 2.2 .cancel-time-text (🆕 新增)
```scss
.cancel-time-text {
  color: #d4380d;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4;
}
```

| 属性 | 值 | 说明 |
|------|----|----- |
| `color` | #d4380d | 橙红色文字 |
| `font-weight` | 500 | 中等字重 |
| `font-size` | 14px | 字体大小 |
| `line-height` | 1.4 | 行高 |

### 3. 响应式样式对照

#### 3.1 小屏幕适配 (≤320px)
```scss
@media (max-width: 320px) {
  .cancel-time-item {
    padding: 6px;
    margin: 6px 0;
    
    &::before {
      font-size: 9px;
      padding: 1px 4px;
    }
  }
  
  .cancel-time-text {
    font-size: 13px;
  }
}
```

#### 3.2 大屏幕适配 (≥414px)
```scss
@media (min-width: 414px) {
  .cancel-time-item {
    padding: 10px;
    margin: 10px 0;
  }
  
  .cancel-time-text {
    font-size: 15px;
  }
}
```

## 🔧 逻辑组件对照

### 1. 计算属性

| 计算属性名 | 返回类型 | 依赖数据 | 说明 |
|------------|----------|----------|------|
| `shouldShowCancelTime` | Boolean | `orderDetail.status`, `orderDetail.cancelTime` | 判断是否显示取消时间 |

```javascript
computed: {
  shouldShowCancelTime() {
    return this.orderDetail.status === 'CANCELLED' && 
           this.orderDetail.cancelTime
  }
}
```

### 2. 方法函数

| 方法名 | 参数 | 返回类型 | 说明 |
|--------|------|----------|------|
| `getCancelTimeDisplay` | 无 | String | 获取格式化的取消时间显示文本 |
| `getRelativeCancelTime` | 无 | String | 获取相对时间描述 |
| `copyOrderNo` | 无 | void | 复制订单号到剪贴板 |

```javascript
methods: {
  getCancelTimeDisplay() {
    if (!this.shouldShowCancelTime) return ''
    return this.orderDetail.getCancelTimeDisplay()
  },
  
  getRelativeCancelTime() {
    if (!this.shouldShowCancelTime) return ''
    return this.orderDetail.cancelTimeRelative
  },
  
  copyOrderNo() {
    // 复制订单号逻辑
  }
}
```

### 3. 生命周期钩子

| 钩子名称 | 触发时机 | 主要作用 |
|----------|----------|----------|
| `mounted` | 组件挂载后 | 加载订单详情数据 |
| `beforeUnmount` | 组件卸载前 | 清理定时器等资源 |

```javascript
async mounted() {
  await this.loadOrderDetail()
},

beforeUnmount() {
  // 清理资源
}
```

## 📱 移动端适配对照

### 1. 屏幕尺寸适配

| 设备类型 | 屏幕宽度 | 适配策略 | 主要调整 |
|----------|----------|----------|----------|
| iPhone SE | 320px | 紧凑布局 | 减小内边距和字体 |
| iPhone 6/7/8 | 375px | 标准布局 | 默认样式 |
| iPhone 6/7/8 Plus | 414px | 宽松布局 | 增大内边距和字体 |

### 2. 交互适配

| 交互类型 | 适配方案 | 说明 |
|----------|----------|------|
| 触摸点击 | 44px最小点击区域 | 确保按钮可点击性 |
| 文字选择 | 支持长按选择 | 允许复制取消时间 |
| 滚动浏览 | 流畅滚动 | 优化滚动性能 |

### 3. 性能优化对照

| 优化项 | 实现方案 | 效果 |
|--------|----------|------|
| 条件渲染 | `v-if` 指令 | 减少DOM节点 |
| 计算缓存 | `computed` 属性 | 避免重复计算 |
| 样式复用 | CSS类继承 | 减少样式冗余 |

## 🧪 测试组件对照

### 1. 单元测试

| 测试组件 | 测试文件 | 测试重点 |
|----------|----------|----------|
| OrderDetail组件 | `OrderDetail.spec.js` | 组件渲染和交互 |
| 取消时间显示 | `CancelTime.spec.js` | 条件显示逻辑 |
| 时间格式化 | `Tempo.spec.js` | 工具函数功能 |

### 2. 集成测试

| 测试场景 | 测试数据 | 预期结果 |
|----------|----------|----------|
| 已取消订单 | `status: 'CANCELLED'` | 显示取消时间 |
| 正常订单 | `status: 'CONFIRMED'` | 不显示取消时间 |
| 数据缺失 | `cancelTime: null` | 优雅降级 |

### 3. 视觉测试

| 测试项目 | 验证内容 | 工具 |
|----------|----------|------|
| 样式一致性 | 与设计稿对比 | 视觉回归测试 |
| 响应式布局 | 不同屏幕尺寸 | 设备模拟器 |
| 交互状态 | 点击、悬停效果 | 手动测试 |

## 📊 组件依赖关系图

```mermaid
graph TD
    A[OrderDetail.vue] --> B[van-cell-group]
    A --> C[OrderDetailInfoModel]
    A --> D[Tempo工具类]
    
    B --> E[van-cell]
    E --> F[order-info容器]
    
    F --> G[info-item基础项]
    F --> H[cancel-time-item新增项]
    
    H --> I[cancel-time-text文本]
    
    C --> J[OrderEntity]
    C --> K[cancelTime字段]
    
    D --> L[formatCancelTime方法]
    D --> M[getRelativeCancelTime方法]
    
    style H fill:#fff7e6,stroke:#ffa940,stroke-width:2px
    style I fill:#fff7e6,stroke:#ffa940,stroke-width:2px
    style K fill:#fff7e6,stroke:#ffa940,stroke-width:2px
    style L fill:#fff7e6,stroke:#ffa940,stroke-width:2px
    style M fill:#fff7e6,stroke:#ffa940,stroke-width:2px
```

## ⚠️ 注意事项

### 1. 组件使用注意事项
- 确保Vant组件库版本兼容性（需要v3.x）
- 注意自定义样式与Vant默认样式的优先级
- 保持组件结构的语义化和可访问性

### 2. 样式注意事项
- 使用CSS变量便于主题切换
- 确保响应式断点的合理性
- 注意浏览器兼容性（支持CSS Grid和Flexbox）

### 3. 数据绑定注意事项
- 处理数据为空或异常的情况
- 确保时间格式化的准确性
- 注意计算属性的依赖关系

### 4. 性能注意事项
- 避免在模板中进行复杂计算
- 合理使用v-if和v-show
- 注意组件的内存泄漏问题
