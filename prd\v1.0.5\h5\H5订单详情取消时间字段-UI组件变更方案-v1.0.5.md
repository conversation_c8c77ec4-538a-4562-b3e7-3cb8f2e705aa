# H5订单详情取消时间字段 - UI组件变更方案 v1.0.5

## 📋 文档信息

| 项目 | 内容 |
|------|------|
| **文档标题** | H5订单详情取消时间字段 - UI组件变更方案 |
| **版本号** | v1.0.5 |
| **平台** | H5移动端 |
| **创建日期** | 2025-07-30 |
| **文档类型** | UI组件变更方案 |

## 🎯 变更概述

### 核心变更
在H5订单详情页面的订单基本信息区域新增取消时间字段显示，提升已取消订单信息的完整性。

### 变更范围
- **OrderDetail.vue** 主组件UI结构调整
- **样式文件** 新增取消时间字段样式
- **交互逻辑** 条件显示和数据绑定
- **响应式设计** 移动端适配优化

## 🎨 UI设计规范

### 1. 视觉设计要求

#### 1.1 布局位置
- **位置**: 订单基本信息区域，位于"用车时间"和"订单号"之间
- **对齐方式**: 左对齐，与其他信息字段保持一致
- **间距**: 上下间距8px，左右内边距8px

#### 1.2 视觉样式
- **背景色**: #fff7e6（浅橙色背景）
- **边框**: 2px solid #ffa940（橙色边框）
- **圆角**: 6px
- **文字颜色**: #d4380d（橙红色）
- **字体大小**: 14px
- **字体粗细**: 500（中等粗细）

#### 1.3 状态标识
- **NEW标签**: 右上角显示"NEW"标识
- **标签背景**: #ff4d4f（红色）
- **标签文字**: 白色，10px字体
- **标签位置**: 绝对定位，top: -8px, right: 8px

### 2. 组件结构设计

#### 2.1 HTML结构
```html
<!-- 取消时间字段容器 -->
<div v-if="shouldShowCancelTime" class="info-item cancel-time-item">
  <span class="cancel-time-text">
    {{ getCancelTimeDisplay() }}
  </span>
</div>
```

#### 2.2 完整组件结构
```vue
<template>
  <div class="order-detail">
    <!-- 订单基本信息区域 -->
    <van-cell-group>
      <van-cell>
        <div class="order-info">
          <!-- 下单时间 -->
          <div class="info-item">
            下单：{{ orderDetail.orderTimeStr }}
            <span class="creator">创建人：{{ orderDetail.creatorName }}</span>
          </div>
          
          <!-- 用车时间 -->
          <div class="info-item">
            用车：{{ orderDetail.startTimeStr }} 至 {{ orderDetail.endTimeStr }}, {{ orderDetail.durationStr }}
          </div>
          
          <!-- 🆕 新增：取消时间字段 -->
          <div v-if="shouldShowCancelTime" class="info-item cancel-time-item">
            <span class="cancel-time-text">
              {{ getCancelTimeDisplay() }}
            </span>
          </div>
          
          <!-- 订单号 -->
          <div class="info-item">
            <span>订单号：{{ orderDetail.orderNo }}</span>
            <van-button size="mini" @click="copyOrderNo">复制</van-button>
          </div>
        </div>
      </van-cell>
    </van-cell-group>
    
    <!-- 其他现有内容保持不变 -->
  </div>
</template>
```

### 3. 样式实现

#### 3.1 SCSS样式定义
```scss
<style lang="scss" scoped>
// 🆕 新增：取消时间字段样式
.cancel-time-item {
  background: #fff7e6;
  border: 2px solid #ffa940;
  border-radius: 6px;
  padding: 8px;
  margin: 8px 0;
  position: relative;
  
  // NEW功能标识
  &::before {
    content: "NEW";
    position: absolute;
    top: -8px;
    right: 8px;
    background: #ff4d4f;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    z-index: 1;
  }
}

.cancel-time-text {
  color: #d4380d;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4;
}

// 现有样式保持不变
.order-info {
  .info-item {
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
    
    .creator {
      color: #666;
      margin-left: 8px;
    }
  }
}

// 响应式适配
@media (max-width: 320px) {
  .cancel-time-item {
    padding: 6px;
    margin: 6px 0;
    
    &::before {
      font-size: 9px;
      padding: 1px 4px;
    }
  }
  
  .cancel-time-text {
    font-size: 13px;
  }
}
</style>
```

#### 3.2 CSS变量定义
```scss
// 取消状态主题色彩
:root {
  --cancel-bg-color: #fff7e6;      // 取消时间背景色
  --cancel-border-color: #ffa940;  // 取消时间边框色
  --cancel-text-color: #d4380d;    // 取消时间文字色
  --new-badge-color: #ff4d4f;      // NEW标识背景色
}

.cancel-time-item {
  background: var(--cancel-bg-color);
  border: 2px solid var(--cancel-border-color);
  
  &::before {
    background: var(--new-badge-color);
  }
}

.cancel-time-text {
  color: var(--cancel-text-color);
}
```

### 4. 交互逻辑实现

#### 4.1 Vue组件逻辑
```javascript
<script>
import { OrderDetailInfoModel } from '@/models/OrderDetailInfoModel'
import { Tempo } from '@/utils/tempo'

export default {
  name: 'OrderDetail',
  data() {
    return {
      orderDetail: new OrderDetailInfoModel()
    }
  },
  computed: {
    // 🆕 新增：判断是否显示取消时间
    shouldShowCancelTime() {
      return this.orderDetail.status === 'CANCELLED' && 
             this.orderDetail.cancelTime
    }
  },
  methods: {
    // 🆕 新增：获取取消时间显示文本
    getCancelTimeDisplay() {
      if (!this.shouldShowCancelTime) return ''
      return this.orderDetail.getCancelTimeDisplay()
    },
    
    // 🆕 新增：获取相对取消时间
    getRelativeCancelTime() {
      if (!this.shouldShowCancelTime) return ''
      return this.orderDetail.cancelTimeRelative
    },
    
    // 现有方法保持不变
    copyOrderNo() {
      // 复制订单号逻辑
    },
    
    async loadOrderDetail() {
      // 加载订单详情逻辑
    }
  }
}
</script>
```

#### 4.2 条件渲染逻辑
```javascript
// 显示条件判断
const shouldShowCancelTime = computed(() => {
  return orderDetail.value.status === 'CANCELLED' && 
         orderDetail.value.cancelTime &&
         orderDetail.value.cancelTime !== null &&
         orderDetail.value.cancelTime !== ''
})

// 错误处理
const getCancelTimeDisplay = () => {
  try {
    if (!shouldShowCancelTime.value) return ''
    
    const formatted = Tempo.formatCancelTime(orderDetail.value.cancelTime)
    return formatted ? `取消：${formatted}` : ''
  } catch (error) {
    console.error('Error displaying cancel time:', error)
    return '取消时间格式错误'
  }
}
```

### 5. 组件集成方案

#### 5.1 Vant组件库集成
```vue
<template>
  <!-- 使用Vant的Cell组件 -->
  <van-cell-group>
    <van-cell>
      <div class="order-basic-info">
        <!-- 基础信息 -->
        <div class="info-row">
          <span>下单：{{ orderDetail.orderTimeStr }}</span>
          <span class="creator">创建人：{{ orderDetail.creatorName }}</span>
        </div>
        
        <div class="info-row">
          <span>用车：{{ orderDetail.startTimeStr }} 至 {{ orderDetail.endTimeStr }}</span>
        </div>
        
        <!-- 🆕 使用Vant的NoticeBar组件样式 -->
        <van-notice-bar
          v-if="shouldShowCancelTime"
          :text="getCancelTimeDisplay()"
          mode="closeable"
          color="#d4380d"
          background="#fff7e6"
          left-icon="warning-o"
          class="cancel-time-notice"
        />
        
        <div class="info-row">
          <span>订单号：{{ orderDetail.orderNo }}</span>
          <van-button size="mini" @click="copyOrderNo">复制</van-button>
        </div>
      </div>
    </van-cell>
  </van-cell-group>
</template>

<script>
import { NoticeBar, Cell, CellGroup, Button } from 'vant'

export default {
  components: {
    [NoticeBar.name]: NoticeBar,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Button.name]: Button
  }
}
</script>

<style lang="scss" scoped>
.cancel-time-notice {
  margin: 8px 0;
  border-radius: 6px;
  border: 2px solid #ffa940;
  position: relative;
  
  &::before {
    content: "NEW";
    position: absolute;
    top: -8px;
    right: 8px;
    background: #ff4d4f;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    z-index: 1;
  }
}
</style>
```

### 6. 响应式设计

#### 6.1 移动端适配
```scss
// 基础移动端适配
.cancel-time-item {
  // 默认样式（375px宽度基准）
  padding: 8px;
  margin: 8px 0;
  font-size: 14px;
  
  // 小屏幕适配（320px及以下）
  @media (max-width: 320px) {
    padding: 6px;
    margin: 6px 0;
    
    .cancel-time-text {
      font-size: 13px;
    }
    
    &::before {
      font-size: 9px;
      padding: 1px 4px;
    }
  }
  
  // 大屏幕适配（414px及以上）
  @media (min-width: 414px) {
    padding: 10px;
    margin: 10px 0;
    
    .cancel-time-text {
      font-size: 15px;
    }
  }
}
```

#### 6.2 横竖屏适配
```scss
// 横屏模式适配
@media (orientation: landscape) {
  .cancel-time-item {
    margin: 6px 0;
    
    .cancel-time-text {
      font-size: 13px;
    }
  }
}

// 竖屏模式适配
@media (orientation: portrait) {
  .cancel-time-item {
    margin: 8px 0;
    
    .cancel-time-text {
      font-size: 14px;
    }
  }
}
```

### 7. 动画效果

#### 7.1 入场动画
```scss
.cancel-time-item {
  // 入场动画
  animation: slideInDown 0.3s ease-out;
  
  &::before {
    // NEW标签动画
    animation: bounceIn 0.5s ease-out 0.2s both;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
```

#### 7.2 交互动画
```scss
.cancel-time-item {
  transition: all 0.2s ease;
  
  // 点击效果
  &:active {
    transform: scale(0.98);
    background: #fff1d6;
  }
  
  // 悬停效果（支持hover的设备）
  @media (hover: hover) {
    &:hover {
      box-shadow: 0 2px 8px rgba(255, 169, 64, 0.3);
    }
  }
}
```

### 8. 可访问性优化

#### 8.1 ARIA标签
```html
<div 
  v-if="shouldShowCancelTime" 
  class="info-item cancel-time-item"
  role="alert"
  aria-label="订单取消时间信息"
  :aria-describedby="`cancel-time-${orderDetail.orderNo}`"
>
  <span 
    class="cancel-time-text"
    :id="`cancel-time-${orderDetail.orderNo}`"
  >
    {{ getCancelTimeDisplay() }}
  </span>
</div>
```

#### 8.2 语义化标签
```html
<section class="order-basic-info" aria-labelledby="order-info-title">
  <h3 id="order-info-title" class="sr-only">订单基本信息</h3>
  
  <div class="info-item">
    <time :datetime="orderDetail.orderTime">
      下单：{{ orderDetail.orderTimeStr }}
    </time>
    <span class="creator">创建人：{{ orderDetail.creatorName }}</span>
  </div>
  
  <div class="info-item">
    <span>用车：</span>
    <time :datetime="orderDetail.startTime">{{ orderDetail.startTimeStr }}</time>
    <span> 至 </span>
    <time :datetime="orderDetail.endTime">{{ orderDetail.endTimeStr }}</time>
  </div>
  
  <!-- 取消时间 -->
  <div v-if="shouldShowCancelTime" class="info-item cancel-time-item" role="alert">
    <time :datetime="orderDetail.cancelTime" class="cancel-time-text">
      {{ getCancelTimeDisplay() }}
    </time>
  </div>
</section>
```

### 9. 性能优化

#### 9.1 计算属性缓存
```javascript
computed: {
  // 缓存显示条件判断
  shouldShowCancelTime() {
    return this.orderDetail.status === 'CANCELLED' && 
           this.orderDetail.cancelTime
  },
  
  // 缓存格式化结果
  cancelTimeDisplay() {
    if (!this.shouldShowCancelTime) return ''
    return this.orderDetail.getCancelTimeDisplay()
  },
  
  // 缓存相对时间
  relativeCancelTime() {
    if (!this.shouldShowCancelTime) return ''
    return this.orderDetail.cancelTimeRelative
  }
}
```

#### 9.2 条件渲染优化
```vue
<template>
  <!-- 使用v-show替代v-if减少DOM操作（如果频繁切换） -->
  <div 
    v-show="shouldShowCancelTime" 
    class="info-item cancel-time-item"
  >
    <span class="cancel-time-text">
      {{ cancelTimeDisplay }}
    </span>
  </div>
</template>
```

### 10. 测试支持

#### 10.1 测试标识
```html
<div 
  v-if="shouldShowCancelTime" 
  class="info-item cancel-time-item"
  data-testid="cancel-time-field"
  data-order-status="cancelled"
>
  <span 
    class="cancel-time-text"
    data-testid="cancel-time-text"
  >
    {{ getCancelTimeDisplay() }}
  </span>
</div>
```

#### 10.2 组件测试用例
```javascript
// 组件渲染测试
describe('OrderDetail Cancel Time', () => {
  test('显示已取消订单的取消时间', () => {
    const wrapper = mount(OrderDetail, {
      data() {
        return {
          orderDetail: {
            status: 'CANCELLED',
            cancelTime: '2025-05-19T11:45:00Z'
          }
        }
      }
    })
    
    expect(wrapper.find('[data-testid="cancel-time-field"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="cancel-time-text"]').text()).toContain('取消：')
  })
  
  test('不显示非取消订单的取消时间', () => {
    const wrapper = mount(OrderDetail, {
      data() {
        return {
          orderDetail: {
            status: 'CONFIRMED',
            cancelTime: null
          }
        }
      }
    })
    
    expect(wrapper.find('[data-testid="cancel-time-field"]').exists()).toBe(false)
  })
})
```

## 📊 变更影响评估

### 11.1 UI层面影响
- **布局变化**: 订单基本信息区域高度增加约40px
- **视觉层次**: 新增橙色高亮区域，增强取消状态识别
- **交互体验**: 无新增交互，仅信息展示

### 11.2 性能影响
- **渲染性能**: 新增一个条件渲染节点，影响极小
- **内存占用**: 增加少量CSS样式和DOM节点
- **加载时间**: 无明显影响

### 11.3 兼容性影响
- **浏览器兼容**: 支持所有现代移动浏览器
- **设备兼容**: 适配iPhone 6+和Android 5.0+设备
- **向后兼容**: 完全兼容现有功能

## ⚠️ 注意事项

### 12.1 开发注意事项
- 确保条件渲染逻辑的准确性
- 注意时间格式化的错误处理
- 保持与现有设计风格的一致性

### 12.2 测试注意事项
- 测试不同订单状态下的显示逻辑
- 验证响应式设计在不同设备上的效果
- 检查可访问性标签的正确性

### 12.3 维护注意事项
- 定期检查NEW标识的显示策略
- 监控用户对新功能的反馈
- 根据使用情况优化样式和交互
