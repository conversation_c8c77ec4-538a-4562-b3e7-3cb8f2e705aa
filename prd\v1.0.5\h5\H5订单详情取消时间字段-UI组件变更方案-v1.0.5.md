# H5订单详情取消时间字段 - UI组件变更方案 v1.0.5

## 文档信息

- **项目**: 擎路 SaaS 管理系统
- **功能**: H5订单详情页面新增取消时间字段
- **版本**: v1.0.5
- **平台**: H5 移动端
- **创建时间**: 2025-07-30
- **文档类型**: UI组件变更方案

## 变更概述

基于提供的UI设计图，在H5订单详情页面的订单基本信息区域新增取消时间字段显示，确保与现有设计风格保持完全一致，提供良好的用户体验。

## UI设计分析

### 1. 参考设计图分析

根据提供的订单管理页面截图，订单信息展示结构如下：

```
订单管理                           [取消改签]
├── 下单: 2025-07-07 12:00
├── 用车: 2025-07-07 15:00 至 2025-07-11 15:00, 4天
├── 取消: [红色标识]               ← 新增位置
├── 订单号: 3792163 [复制]
└── 渠道订单号: 112814041807428 [复制]
```

### 2. 设计要点提取

- **布局方式**: 左右对齐的信息展示
- **字体规范**: 标签使用常规字重，内容使用较细字重
- **颜色规范**: 标签为深色，内容为灰色，取消状态为红色
- **间距规范**: 行间距适中，信息层次清晰
- **交互元素**: 部分信息支持复制功能

## 组件变更方案

### 1. 现有组件结构分析

基于代码分析，H5订单详情页面使用以下组件结构：

```vue
<!-- 订单基本信息区域 -->
<div class="order-basic-info">
  <div class="info-row">
    <span class="info-label">下单:</span>
    <span class="info-value">{{ orderDetail.orderInfoVo.orderTimeStr }}</span>
  </div>
  
  <div class="info-row">
    <span class="info-label">用车:</span>
    <span class="info-value">
      {{ orderDetail.orderInfoVo.pickupDateTimeStr }} 至 
      {{ orderDetail.orderInfoVo.returnDateTimeStr }}, 
      {{ orderDetail.orderInfoVo.remainTimeStr }}
    </span>
  </div>
  
  <!-- 其他信息行... -->
</div>
```

### 2. 新增取消时间组件

#### 2.1 组件模板变更

```vue
<template>
  <div class="order-basic-info">
    <!-- 现有信息行 -->
    <div class="info-row">
      <span class="info-label">下单:</span>
      <span class="info-value">{{ orderDetail.orderInfoVo.orderTimeStr }}</span>
    </div>
    
    <div class="info-row">
      <span class="info-label">用车:</span>
      <span class="info-value">
        {{ orderDetail.orderInfoVo.pickupDateTimeStr }} 至 
        {{ orderDetail.orderInfoVo.returnDateTimeStr }}, 
        {{ orderDetail.orderInfoVo.remainTimeStr }}
      </span>
    </div>
    
    <!-- 新增取消时间行 -->
    <div 
      v-if="orderDetail.orderInfoVo.cancelTimeStr" 
      class="info-row cancel-time-row"
    >
      <span class="info-label cancel-label">取消:</span>
      <span class="info-value cancel-value">
        {{ orderDetail.orderInfoVo.cancelTimeStr }}
      </span>
    </div>
    
    <!-- 其他现有信息行 -->
    <div class="info-row">
      <span class="info-label">订单号:</span>
      <span class="info-value">
        {{ orderDetail.orderInfoVo.orderId }}
        <van-icon name="copy" class="copy-icon" @click="copyOrderId" />
      </span>
    </div>
  </div>
</template>
```

#### 2.2 样式定义

```less
// 基础信息行样式
.order-basic-info {
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 12px;
  
  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 8px 0;
    min-height: 24px;
    
    &:not(:last-child) {
      border-bottom: 1px solid #f5f5f5;
    }
    
    .info-label {
      color: #333333;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      flex-shrink: 0;
      margin-right: 12px;
    }
    
    .info-value {
      color: #666666;
      font-size: 14px;
      font-weight: 300;
      line-height: 20px;
      text-align: right;
      flex: 1;
      word-break: break-all;
    }
  }
  
  // 取消时间特殊样式
  .cancel-time-row {
    .cancel-label {
      color: #ff4d4f; // 红色标签
      font-weight: 500; // 稍微加粗突出
    }
    
    .cancel-value {
      color: #666666; // 保持与其他值一致的颜色
    }
  }
  
  // 复制图标样式
  .copy-icon {
    margin-left: 8px;
    color: #1890ff;
    font-size: 16px;
    cursor: pointer;
    
    &:hover {
      color: #40a9ff;
    }
  }
}
```

### 3. 响应式适配

#### 3.1 移动端适配

```less
// 移动端样式适配
@media (max-width: 768px) {
  .order-basic-info {
    padding: 12px;
    margin-bottom: 8px;
    
    .info-row {
      padding: 6px 0;
      
      .info-label {
        font-size: 13px;
        min-width: 60px; // 确保标签宽度一致
      }
      
      .info-value {
        font-size: 13px;
        margin-left: 8px;
      }
    }
    
    // 长文本换行处理
    .cancel-time-row .info-value {
      white-space: nowrap;
    }
  }
}

// 小屏幕适配
@media (max-width: 375px) {
  .order-basic-info {
    .info-row {
      flex-direction: column;
      align-items: flex-start;
      
      .info-label {
        margin-bottom: 4px;
      }
      
      .info-value {
        text-align: left;
        margin-left: 0;
      }
    }
  }
}
```

### 4. 状态管理

#### 4.1 显示逻辑控制

```javascript
// 组件逻辑部分
export default {
  computed: {
    // 是否显示取消时间
    shouldShowCancelTime() {
      return this.orderDetail?.orderInfoVo?.cancelTimeStr && 
             this.orderDetail?.orderInfoVo?.orderStatus === 8
    },
    
    // 取消时间显示文本
    cancelTimeDisplay() {
      if (!this.shouldShowCancelTime) return ''
      return this.orderDetail.orderInfoVo.cancelTimeStr
    }
  },
  
  methods: {
    // 复制订单号功能
    copyOrderId() {
      const orderId = this.orderDetail?.orderInfoVo?.orderId
      if (orderId) {
        // 使用现有的复制工具
        this.$copyText(orderId).then(() => {
          this.$toast('订单号已复制')
        })
      }
    }
  }
}
```

### 5. 交互设计

#### 5.1 动画效果

```less
// 取消时间行的进入动画
.cancel-time-row {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 状态变化时的过渡效果
.info-row {
  transition: all 0.2s ease-in-out;
}
```

#### 5.2 加载状态

```vue
<template>
  <!-- 加载状态显示 -->
  <div v-if="loading" class="info-row">
    <span class="info-label">取消:</span>
    <van-skeleton title :row="0" class="cancel-skeleton" />
  </div>
  
  <!-- 正常状态显示 -->
  <div 
    v-else-if="shouldShowCancelTime" 
    class="info-row cancel-time-row"
  >
    <span class="info-label cancel-label">取消:</span>
    <span class="info-value cancel-value">
      {{ cancelTimeDisplay }}
    </span>
  </div>
</template>
```

### 6. 可访问性优化

#### 6.1 语义化标记

```vue
<template>
  <div class="order-basic-info" role="region" aria-label="订单基本信息">
    <!-- 取消时间行 -->
    <div 
      v-if="shouldShowCancelTime" 
      class="info-row cancel-time-row"
      role="listitem"
    >
      <span 
        class="info-label cancel-label"
        id="cancel-time-label"
      >
        取消:
      </span>
      <span 
        class="info-value cancel-value"
        aria-labelledby="cancel-time-label"
        :aria-label="`订单取消时间: ${cancelTimeDisplay}`"
      >
        {{ cancelTimeDisplay }}
      </span>
    </div>
  </div>
</template>
```

#### 6.2 屏幕阅读器支持

```less
// 为屏幕阅读器提供额外信息
.cancel-time-row {
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}
```

## 组件集成方案

### 1. 现有组件修改

基于现有的订单详情组件，需要进行以下修改：

1. **模板更新**: 在订单信息展示区域添加取消时间行
2. **样式扩展**: 新增取消时间相关的样式定义
3. **逻辑增强**: 添加取消时间的显示控制逻辑

### 2. 兼容性保证

- **向后兼容**: 新增功能不影响现有功能
- **数据兼容**: 支持有无取消时间数据的订单
- **样式兼容**: 与现有设计风格完全一致

### 3. 测试覆盖

- **显示测试**: 验证不同状态下的显示效果
- **样式测试**: 确保在不同设备上的显示一致性
- **交互测试**: 验证动画和过渡效果
- **可访问性测试**: 确保屏幕阅读器兼容性

## 实施计划

### 1. 开发阶段

1. **第一阶段**: 基础模板和样式实现
2. **第二阶段**: 交互逻辑和动画效果
3. **第三阶段**: 响应式适配和可访问性优化
4. **第四阶段**: 测试和优化

### 2. 质量保证

- 严格按照设计图实现像素级还原
- 确保与现有组件风格完全一致
- 通过多设备测试验证兼容性
- 进行用户体验测试

---

**备注**: 本UI组件变更方案基于擎路SaaS系统现有设计规范，确保新增功能与现有界面的完美融合。开发时请严格按照设计规范实施，保证用户体验的一致性。
