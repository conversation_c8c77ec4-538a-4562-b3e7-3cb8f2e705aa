<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短租订单列表复制功能 - 擎路PC</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/reset.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/antd.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ant-design/icons@5.2.6/lib/index.css">
    <style>
        /* 页面特定样式，确保与线上系统完全一致 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto <PERSON>', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 200px;
            background-color: #001529;
            color: white;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            background-color: white;
            padding: 0 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }

        .content {
            flex: 1;
            padding: 24px;
            background-color: #f5f5f5;
        }

        .page-header {
            background-color: white;
            padding: 16px 24px;
            margin-bottom: 16px;
            border-radius: 6px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }

        .search-form {
            background-color: white;
            padding: 24px;
            margin-bottom: 16px;
            border-radius: 6px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }

        .table-container {
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
            overflow: hidden;
        }

        .table-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-tabs {
            display: flex;
            gap: 8px;
        }

        .table-tab {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background-color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .table-tab.active {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .table-actions {
            display: flex;
            gap: 8px;
        }

        .ant-table {
            font-size: 14px;
        }

        .ant-table-thead > tr > th {
            background-color: #fafafa;
            font-weight: 500;
            color: #262626;
            border-bottom: 1px solid #f0f0f0;
        }

        .ant-table-tbody > tr > td {
            border-bottom: 1px solid #f0f0f0;
            padding: 12px 16px;
        }

        .ant-table-tbody > tr:hover > td {
            background-color: #f5f5f5;
        }

        /* 复制图标样式 */
        .copy-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            margin-left: 4px;
            cursor: pointer;
            color: #1890ff;
            transition: all 0.3s;
            border-radius: 2px;
        }

        .copy-icon:hover {
            background-color: #e6f7ff;
            color: #40a9ff;
        }

        .copy-icon svg {
            width: 12px;
            height: 12px;
        }

        /* 改动点标注样式 */
        .change-highlight {
            background-color: #fff7e6;
            border: 2px solid #faad14;
            border-radius: 4px;
            padding: 2px;
            position: relative;
        }

        .change-highlight::after {
            content: "新增复制功能";
            position: absolute;
            top: -8px;
            left: 8px;
            background-color: #faad14;
            color: white;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 2px;
            white-space: nowrap;
        }

        /* 订单信息样式 */
        .order-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .order-number {
            display: flex;
            align-items: center;
            gap: 4px;
            font-weight: 500;
        }

        .channel-order-number {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #666;
            font-size: 13px;
        }

        .vehicle-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .vehicle-number {
            display: flex;
            align-items: center;
            gap: 4px;
            font-weight: 500;
            color: #1890ff;
        }

        .vehicle-model {
            color: #666;
            font-size: 13px;
        }

        /* 状态标签样式 */
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-confirmed {
            background-color: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }

        .status-arranged {
            background-color: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .status-picked {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-returned {
            background-color: #f9f0ff;
            color: #722ed1;
            border: 1px solid #d3adf7;
        }

        /* 操作按钮样式 */
        .action-buttons {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 2px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background-color: white;
            color: #666;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .action-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            padding: 16px;
            background-color: white;
            border-top: 1px solid #f0f0f0;
        }

        .pagination-item {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            background-color: white;
            cursor: pointer;
            margin: 0 2px;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .pagination-item:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .pagination-item.active {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .pagination-item.disabled {
            color: #d9d9d9;
            cursor: not-allowed;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 160px;
            }
        }

        @media (max-width: 768px) {
            .layout {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .content {
                padding: 16px;
            }
            
            .table-header {
                flex-direction: column;
                gap: 16px;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div style="padding: 16px; border-bottom: 1px solid #002140;">
                <h3 style="margin: 0; color: white;">擎路管理系统</h3>
            </div>
            <nav style="padding: 16px 0;">
                <div style="padding: 12px 16px; background-color: #1890ff; color: white; margin-bottom: 8px;">
                    <span>订单</span>
                </div>
                <div style="padding: 8px 16px 8px 32px; color: #1890ff; background-color: #001529;">
                    <span>短租订单</span>
                </div>
                <div style="padding: 8px 16px 8px 32px; color: #8c8c8c;">
                    <span>长租订单</span>
                </div>
                <div style="padding: 8px 16px 8px 32px; color: #8c8c8c;">
                    <span>电子合同</span>
                </div>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 头部 -->
            <div class="header">
                <div style="display: flex; align-items: center; gap: 16px;">
                    <h2 style="margin: 0; font-size: 18px;">短租订单</h2>
                </div>
                <div style="display: flex; align-items: center; gap: 16px;">
                    <span>企鹅123</span>
                    <button style="padding: 4px 12px; border: 1px solid #d9d9d9; border-radius: 4px; background: white; cursor: pointer;">退出</button>
                </div>
            </div>

            <!-- 内容区 -->
            <div class="content">
                <!-- 搜索表单 -->
                <div class="search-form">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 16px;">
                        <div>
                            <label style="display: block; margin-bottom: 4px; color: #262626;">订单信息 :</label>
                            <input type="text" placeholder="请填写" style="width: 100%; padding: 8px 12px; border: 1px solid #d9d9d9; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 4px; color: #262626;">订单来源 :</label>
                            <select style="width: 100%; padding: 8px 12px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                <option>全部</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 4px; color: #262626;">车型 :</label>
                            <select style="width: 100%; padding: 8px 12px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                <option>ID/名称</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 4px; color: #262626;">订单状态 :</label>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                <span class="status-tag status-confirmed">已确认</span>
                                <span class="status-tag status-arranged">已排车</span>
                                <span class="status-tag status-picked">已取车</span>
                                <span class="status-tag status-returned">已还车</span>
                            </div>
                        </div>
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button style="padding: 8px 16px; background-color: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">查 询</button>
                        <button style="padding: 8px 16px; background-color: white; color: #666; border: 1px solid #d9d9d9; border-radius: 4px; cursor: pointer;">重 置</button>
                    </div>
                </div>

                <!-- 表格容器 -->
                <div class="table-container">
                    <!-- 表格头部 -->
                    <div class="table-header">
                        <div class="table-tabs">
                            <div class="table-tab active">全部(188)</div>
                            <div class="table-tab">今日待取车(0)</div>
                            <div class="table-tab">今日待还车(0)</div>
                            <div class="table-tab">今日待排车(0)</div>
                            <div class="table-tab">近2日待取车(0)</div>
                            <div class="table-tab">近2日待还车(0)</div>
                            <div class="table-tab">逾期未取车(131)</div>
                            <div class="table-tab">逾期未还车(12)</div>
                            <div class="table-tab">待确认还车(1)</div>
                            <div class="table-tab">新订单(0)</div>
                        </div>
                        <div class="table-actions">
                            <button style="padding: 6px 12px; background-color: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">新增订单</button>
                            <button style="padding: 6px 12px; background-color: white; color: #666; border: 1px solid #d9d9d9; border-radius: 4px; cursor: pointer; font-size: 14px;">导出订单</button>
                            <button style="padding: 6px 12px; background-color: white; color: #666; border: 1px solid #d9d9d9; border-radius: 4px; cursor: pointer; font-size: 14px;">新增标签</button>
                        </div>
                    </div>

                    <!-- 表格 -->
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background-color: #fafafa;">
                                <th style="padding: 12px 16px; text-align: left; border-bottom: 1px solid #f0f0f0; font-weight: 500;">创建时间</th>
                                <th style="padding: 12px 16px; text-align: left; border-bottom: 1px solid #f0f0f0; font-weight: 500;">状态</th>
                                <th style="padding: 12px 16px; text-align: left; border-bottom: 1px solid #f0f0f0; font-weight: 500;">预订车辆</th>
                                <th style="padding: 12px 16px; text-align: left; border-bottom: 1px solid #f0f0f0; font-weight: 500;">自定义标签</th>
                                <th style="padding: 12px 16px; text-align: left; border-bottom: 1px solid #f0f0f0; font-weight: 500;">取还</th>
                                <th style="padding: 12px 16px; text-align: left; border-bottom: 1px solid #f0f0f0; font-weight: 500;">承租人</th>
                                <th style="padding: 12px 16px; text-align: left; border-bottom: 1px solid #f0f0f0; font-weight: 500;">订单总价</th>
                                <th style="padding: 12px 16px; text-align: left; border-bottom: 1px solid #f0f0f0; font-weight: 500;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 第一行数据 -->
                            <tr>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="order-info">
                                        <div>2025-07-18 10:25</div>
                                        <div>来源：订单</div>
                                        <div class="channel-order-number change-highlight">
                                            渠道订单号：-
                                            <span class="copy-icon" title="复制渠道订单号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H196V196h464v536z"/>
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="order-number change-highlight">
                                            订单号1705946
                                            <span class="copy-icon" title="复制订单号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H196V196h464v536z"/>
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="display: flex; flex-direction: column; gap: 4px;">
                                        <span class="status-tag status-picked">已取车</span>
                                        <div>取车：sjz-admin</div>
                                        <div>还车：待排司机</div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="vehicle-info">
                                        <div class="vehicle-number change-highlight">
                                            琼BF09400
                                            <span class="copy-icon" title="复制车牌号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H196V196h464v536z"/>
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="vehicle-model">1376-大众 迈腾GTE插电混动 2020款 GTE 豪华型 有天窗 普牌</div>
                                        <div style="color: #ff4d4f; font-size: 12px;">押金未收</div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;"></td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="display: flex; flex-direction: column; gap: 4px;">
                                        <div style="font-weight: 500;">预计 2025-07-18 11:00 至 2025-07-31 17:00</div>
                                        <div style="color: #666;">13天 7小时</div>
                                        <div>取车：神风租车22(到店取车)</div>
                                        <div>云南普洱梅子湖公园公园分店</div>
                                        <div>还车：神风租车22(到店还车)</div>
                                        <div>云南普洱梅子湖公园公园分店</div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="display: flex; flex-direction: column; gap: 4px;">
                                        <div>sjz-admin</div>
                                        <div>13627003158</div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="color: #ff4d4f; font-weight: 500;">￥29</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="action-buttons">
                                        <button class="action-btn">排司机</button>
                                        <button class="action-btn">还车</button>
                                        <button class="action-btn">强制改排</button>
                                        <button class="action-btn">添加标签</button>
                                    </div>
                                </td>
                            </tr>

                            <!-- 第二行数据 -->
                            <tr>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="order-info">
                                        <div>2025-07-15 16:17</div>
                                        <div>来源：线下渠道订单</div>
                                        <div class="channel-order-number change-highlight">
                                            渠道订单号：-
                                            <span class="copy-icon" title="复制渠道订单号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H196V196h464v536z"/>
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="order-number change-highlight">
                                            订单号1705915
                                            <span class="copy-icon" title="复制订单号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H196V196h464v536z"/>
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="display: flex; flex-direction: column; gap: 4px;">
                                        <span class="status-tag status-picked">已取车</span>
                                        <div>取车：sjz-admin</div>
                                        <div>还车：待排司机</div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="vehicle-info">
                                        <div class="vehicle-number change-highlight">
                                            琼BF06517
                                            <span class="copy-icon" title="复制车牌号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H196V196h464v536z"/>
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="vehicle-model">1376-大众 迈腾GTE插电混动 2020款 GTE 豪华型 有天窗 普牌</div>
                                        <div style="color: #ff4d4f; font-size: 12px;">押金未收</div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;"></td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="display: flex; flex-direction: column; gap: 4px;">
                                        <div style="font-weight: 500;">预计 2025-07-15 16:17 至 2025-07-16 16:17</div>
                                        <div style="color: #666;">1天</div>
                                        <div>取车：神风租车22(到店取车)</div>
                                        <div>云南普洱梅子湖公园公园分店</div>
                                        <div>还车：神风租车22(到店还车)</div>
                                        <div>云南普洱梅子湖公园公园分店</div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="display: flex; flex-direction: column; gap: 4px;">
                                        <div>sjz-admin</div>
                                        <div>13627003158</div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="color: #ff4d4f; font-weight: 500;">￥3</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="action-buttons">
                                        <button class="action-btn">排司机</button>
                                        <button class="action-btn">还车</button>
                                        <button class="action-btn">强制改排</button>
                                        <button class="action-btn">添加标签</button>
                                    </div>
                                </td>
                            </tr>

                            <!-- 第三行数据（携程订单示例） -->
                            <tr>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="order-info">
                                        <div>2025-05-07 19:59</div>
                                        <div>来源：携程订单</div>
                                        <div class="channel-order-number change-highlight">
                                            渠道订单号：1128168945146302
                                            <span class="copy-icon" title="复制渠道订单号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H196V196h464v536z"/>
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="order-number change-highlight">
                                            订单号1704926
                                            <span class="copy-icon" title="复制订单号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H196V196h464v536z"/>
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="display: flex; flex-direction: column; gap: 4px;">
                                        <span class="status-tag status-arranged">已排车</span>
                                        <div>取车：待排司机</div>
                                        <div>还车：待排司机</div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="vehicle-info">
                                        <div class="vehicle-number change-highlight">
                                            琼BD04610
                                            <span class="copy-icon" title="复制车牌号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H196V196h464v536z"/>
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="vehicle-model">1380-别克 微蓝6 2020款 互联时尚型 PLUS 普牌</div>
                                        <div style="color: #ff4d4f; font-size: 12px;">押金未收</div>
                                        <div style="color: #1890ff; font-size: 12px;">无忧租</div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;"></td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="display: flex; flex-direction: column; gap: 4px;">
                                        <div style="font-weight: 500;">预计 2025-07-01 10:00 至 2025-07-04 10:30</div>
                                        <div style="color: #666;">3天 1小时</div>
                                        <div>取车：神风租车22(到店取车)</div>
                                        <div>云南普洱梅子湖公园公园分店</div>
                                        <div>还车：神风租车22(到店还车)</div>
                                        <div>云南普洱梅子湖公园公园分店</div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="display: flex; flex-direction: column; gap: 4px;">
                                        <div>徐艳</div>
                                        <div>17621905785</div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="color: #ff4d4f; font-weight: 500;">￥659</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="action-buttons">
                                        <button class="action-btn">排司机</button>
                                        <button class="action-btn">改排</button>
                                        <button class="action-btn">取车</button>
                                        <button class="action-btn">强制改排</button>
                                        <button class="action-btn">添加标签</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-item disabled">上一页</div>
                        <div class="pagination-item active">1</div>
                        <div class="pagination-item">2</div>
                        <div class="pagination-item">3</div>
                        <div class="pagination-item">4</div>
                        <div class="pagination-item">5</div>
                        <div class="pagination-item">•••</div>
                        <div class="pagination-item">19</div>
                        <div class="pagination-item">下一页</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 复制功能实现
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有复制图标添加点击事件
            const copyIcons = document.querySelectorAll('.copy-icon');
            
            copyIcons.forEach(icon => {
                icon.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 获取要复制的文本
                    let textToCopy = '';
                    const parentElement = this.parentElement;
                    
                    if (parentElement.classList.contains('order-number')) {
                        textToCopy = parentElement.textContent.replace('复制订单号', '').trim();
                    } else if (parentElement.classList.contains('channel-order-number')) {
                        textToCopy = parentElement.textContent.replace('渠道订单号：', '').replace('复制渠道订单号', '').trim();
                    } else if (parentElement.classList.contains('vehicle-number')) {
                        textToCopy = parentElement.textContent.replace('复制车牌号', '').trim();
                    }
                    
                    // 复制到剪贴板
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(textToCopy).then(() => {
                            showCopySuccess(this);
                        }).catch(err => {
                            console.error('复制失败:', err);
                            fallbackCopyTextToClipboard(textToCopy, this);
                        });
                    } else {
                        fallbackCopyTextToClipboard(textToCopy, this);
                    }
                });
            });
            
            // 备用复制方法
            function fallbackCopyTextToClipboard(text, icon) {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                try {
                    const successful = document.execCommand('copy');
                    if (successful) {
                        showCopySuccess(icon);
                    } else {
                        showCopyError(icon);
                    }
                } catch (err) {
                    console.error('复制失败:', err);
                    showCopyError(icon);
                }
                
                document.body.removeChild(textArea);
            }
            
            // 显示复制成功提示
            function showCopySuccess(icon) {
                const originalColor = icon.style.color;
                icon.style.color = '#52c41a';
                icon.title = '复制成功！';
                
                setTimeout(() => {
                    icon.style.color = originalColor;
                    icon.title = '';
                }, 1000);
            }
            
            // 显示复制失败提示
            function showCopyError(icon) {
                const originalColor = icon.style.color;
                icon.style.color = '#ff4d4f';
                icon.title = '复制失败';
                
                setTimeout(() => {
                    icon.style.color = originalColor;
                    icon.title = '';
                }, 1000);
            }
        });
    </script>
</body>
</html> 