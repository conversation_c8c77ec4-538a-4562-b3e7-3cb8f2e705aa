# H5订单详情新增取消时间字段 PRD

## 1. 需求背景

### 1.1 业务背景
在当前的H5订单管理系统中，已取消的订单缺少取消时间的明确显示，用户和客服人员无法快速了解订单的取消时间，影响了订单管理的效率和用户体验。

### 1.2 用户痛点
- 客服人员在处理已取消订单时，无法快速获知订单的具体取消时间
- 用户查看已取消订单时，缺少取消时间信息，影响订单历史记录的完整性
- 订单审计和数据分析时，缺少取消时间维度的数据支持

### 1.3 解决方案
在H5订单详情页面的订单基本信息区域新增"取消时间"字段，仅在订单状态为"已取消"时显示，提供完整的订单时间线信息。

## 2. 产品目标

### 2.1 核心目标
- 提升已取消订单信息的完整性和透明度
- 改善客服人员处理取消订单的工作效率
- 为订单数据分析提供更完整的时间维度数据

### 2.2 成功指标
- 客服处理已取消订单的平均时间减少20%
- 用户对订单信息完整性的满意度提升
- 取消订单相关的客服咨询量减少15%

## 3. 功能需求

### 3.1 核心功能
**功能名称**: 订单详情页新增取消时间字段

**功能描述**: 在H5订单详情页面的订单基本信息区域，新增显示订单取消时间的字段

### 3.2 详细需求

#### 3.2.1 显示规则
- **显示条件**: 仅当订单状态为"已取消"时显示取消时间字段
- **显示位置**: 订单基本信息区域，位于"用车时间"和"订单号"之间
- **显示格式**: "取消：YYYY-MM-DD HH:mm"
- **数据来源**: 订单取消操作时记录的时间戳

#### 3.2.2 字段规范
- **字段标签**: "取消"
- **时间格式**: YYYY-MM-DD HH:mm（如：2025-05-19 11:45）
- **字体样式**: 与其他时间字段保持一致
- **颜色规范**: 使用警告色调（橙色系）突出显示

#### 3.2.3 交互要求
- 无特殊交互需求，仅作为信息展示
- 支持文本选择和复制功能
- 在不同屏幕尺寸下保持良好的显示效果

## 4. 技术需求

### 4.1 前端实现
- **组件位置**: `modules/h5/src/views/order/OrderDetail.vue`
- **数据模型**: 扩展 `OrderDetailInfoModel` 添加 `cancelTime` 字段
- **样式要求**: 使用 Vant 3 组件库的设计规范
- **响应式**: 适配不同移动设备屏幕尺寸

### 4.2 后端支持
- **数据字段**: 订单实体需包含 `cancelTime` 字段
- **API接口**: 订单详情接口需返回取消时间数据
- **数据格式**: ISO 8601 格式的时间戳

### 4.3 数据处理
- **时间格式化**: 使用 Tempo 工具进行时间格式化
- **空值处理**: 当取消时间为空时不显示该字段
- **时区处理**: 统一使用系统配置的时区

## 5. 设计规范

### 5.1 视觉设计
- **字体大小**: 14px，与其他信息字段保持一致
- **字体颜色**: #d4380d（橙红色），突出显示取消状态
- **背景色**: #fff7e6（浅橙色背景）
- **边框**: 2px solid #ffa940（橙色边框）
- **圆角**: 6px

### 5.2 布局规范
- **位置**: 订单基本信息列表中，位于用车时间下方
- **间距**: 上下间距8px，左右内边距8px
- **对齐**: 左对齐，与其他信息字段保持一致

### 5.3 状态标识
- **新功能标识**: 右上角显示"NEW"标签，用于功能上线初期的用户引导
- **标签样式**: 红色背景，白色文字，10px字体

## 6. 用户体验

### 6.1 信息层级
- 取消时间作为重要的订单状态信息，给予适当的视觉权重
- 通过颜色和背景突出显示，但不影响其他信息的阅读

### 6.2 易用性
- 时间格式清晰易读，符合用户习惯
- 字段标签简洁明了，一目了然
- 与现有时间字段保持一致的交互体验

## 7. 兼容性要求

### 7.1 浏览器兼容
- iOS Safari 12+
- Android Chrome 70+
- 微信内置浏览器
- 支付宝内置浏览器

### 7.2 设备兼容
- iPhone 6及以上机型
- Android 5.0及以上系统
- 屏幕分辨率：320px - 414px宽度

## 8. 测试要求

### 8.1 功能测试
- 已取消订单显示取消时间字段
- 非取消状态订单不显示取消时间字段
- 时间格式正确显示
- 不同时区下的时间显示准确性

### 8.2 兼容性测试
- 多种移动设备的显示效果
- 不同浏览器的兼容性
- 横竖屏切换的适配性

### 8.3 性能测试
- 页面加载时间不受影响
- 大量订单数据下的渲染性能

## 9. 上线计划

### 9.1 开发阶段
- **需求评审**: 1天
- **技术方案设计**: 1天
- **前端开发**: 2天
- **后端开发**: 1天
- **联调测试**: 1天

### 9.2 测试阶段
- **功能测试**: 1天
- **兼容性测试**: 1天
- **用户验收测试**: 1天

### 9.3 发布计划
- **预发布环境验证**: 1天
- **生产环境发布**: 灰度发布，逐步放量
- **监控观察**: 发布后持续监控1周

## 10. 风险评估

### 10.1 技术风险
- **低风险**: 功能相对简单，主要是UI展示逻辑
- **数据一致性**: 确保取消时间数据的准确性和完整性

### 10.2 业务风险
- **用户接受度**: 新增字段可能需要用户适应期
- **性能影响**: 对现有页面性能影响极小

### 10.3 风险缓解
- 充分的测试验证
- 灰度发布策略
- 实时监控和快速回滚机制

## 11. 后续优化

### 11.1 数据分析
- 收集取消时间相关的用户行为数据
- 分析取消时间对业务指标的影响

### 11.2 功能扩展
- 考虑在订单列表页面也显示取消时间
- 增加取消原因与取消时间的关联显示

### 11.3 用户反馈
- 收集用户对新功能的反馈
- 根据反馈持续优化显示效果和交互体验
