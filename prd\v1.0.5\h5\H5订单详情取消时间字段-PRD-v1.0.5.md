# H5订单详情取消时间字段功能 - 产品需求文档 v1.0.5

## 文档信息

- **项目**: 擎路 SaaS 管理系统
- **功能**: H5订单详情页面新增取消时间字段
- **版本**: v1.0.5
- **平台**: H5 移动端
- **创建时间**: 2025-07-30
- **参考原型**: 订单管理页面UI设计图
- **文档类型**: 产品需求文档 (PRD)

## 需求背景

### 1. 业务背景

在当前的H5订单详情页面中，用户可以查看订单的基本信息，包括下单时间、用车时间等关键时间节点。但是当订单状态为"已取消"时，缺少取消时间的显示，导致用户无法了解订单的完整时间线，影响用户体验和客服处理效率。

### 2. 用户痛点

- **用户端**: 无法查看订单取消的具体时间，影响对订单历史的了解
- **客服端**: 处理用户咨询时缺少取消时间信息，降低服务效率
- **运营端**: 无法准确分析订单取消的时间分布规律

### 3. 业务价值

- 提升用户体验，完善订单信息展示
- 提高客服处理效率，减少沟通成本
- 为运营分析提供更完整的数据支持

## 功能概述

### 1. 核心功能

在H5订单详情页面中，当订单状态为"已取消"时，在订单基本信息区域显示取消时间字段，格式为"取消: YYYY-MM-DD HH:mm"。

### 2. 功能范围

- **页面范围**: H5订单详情页面
- **状态范围**: 仅在订单状态为"已取消"时显示
- **数据范围**: 所有包含取消时间的订单记录

## 详细需求

### 1. 功能需求

#### 1.1 显示规则

- **显示条件**: 订单状态为"已取消"且存在取消时间数据
- **显示位置**: 订单基本信息区域，位于用车时间信息下方
- **显示格式**: "取消: YYYY-MM-DD HH:mm"
- **字体样式**: 与现有时间字段保持一致

#### 1.2 数据处理

- **时间格式**: 后端返回标准时间戳，前端格式化显示
- **时区处理**: 按照用户本地时区显示
- **空值处理**: 如果取消时间为空，则不显示该字段

#### 1.3 交互设计

- **静态显示**: 取消时间为只读信息，无交互功能
- **样式一致**: 与现有时间字段样式保持一致
- **响应式**: 适配不同屏幕尺寸

### 2. 非功能需求

#### 2.1 性能要求

- 页面加载时间不受影响
- 数据获取响应时间 < 500ms

#### 2.2 兼容性要求

- 支持iOS Safari 12+
- 支持Android Chrome 70+
- 支持微信内置浏览器

#### 2.3 可用性要求

- 界面简洁清晰，信息层次分明
- 符合现有设计规范和用户习惯

## 技术实现方案

### 1. 数据模型变更

#### 1.1 OrderDetailInfoModel 扩展

基于现有的 `OrderDetailInfoModel` 类，新增取消时间字段的格式化处理：

```javascript:modules/h5/comp-lib/src/services/models/OrderDetailModels/OrderDetailInfoModel.js
// 在现有 OrderDetailInfoModel 类中新增
cancelTimeStr = {
  type: String,
  field: (data) => {
    // 仅在订单状态为已取消(8)且存在取消时间时显示
    if (data.cancelTime && data.orderStatus === 8) {
      return Tempo.format(data.cancelTime, 'MM-dd HH:mm')
    }
    return null
  }
}
```

#### 1.2 OrderBaseInfoModel 扩展

同时在基础信息模型中也需要添加相应字段：

```javascript:modules/h5/comp-lib/src/services/models/OrderDetailModels/OrderBaseInfoModel.js
// 新增取消时间相关字段
cancelTimeStr = {
  type: String,
  field: (data) => {
    if (data.cancelTime && data.orderStatus === 8) {
      return Tempo.format(data.cancelTime, 'MM-dd HH:mm')
    }
    return ''
  }
}

cancelDateStr = {
  type: String,
  field: (data) => {
    if (data.cancelTime && data.orderStatus === 8) {
      return Tempo.format(data.cancelTime)
    }
    return ''
  }
}
```

#### 1.3 后端接口数据结构

```json
{
  "code": 200,
  "data": {
    "orderInfoVo": {
      "orderId": "3792163",
      "orderStatus": 8,
      "orderStatusStr": "已取消",
      "cancelTime": "2025-07-30T15:30:00Z",
      "orderTime": "2025-07-07T12:00:00Z",
      "pickupDate": "2025-07-07T15:00:00Z",
      "returnDate": "2025-07-11T15:00:00Z"
      // ... 其他现有字段
    }
  }
}
```

### 2. 前端组件实现

#### 2.1 订单详情页面组件变更

基于现有的H5订单详情页面组件，在订单基本信息展示区域新增取消时间显示逻辑：

```vue:modules/h5/订单详情页面组件
<template>
  <div class="order-info-section">
    <!-- 现有订单信息 -->
    <div class="info-item">
      <span class="label">下单:</span>
      <span class="value">{{ orderDetail.orderInfoVo.orderTimeStr }}</span>
    </div>

    <div class="info-item">
      <span class="label">用车:</span>
      <span class="value">{{ orderDetail.orderInfoVo.pickupDateTimeStr }} 至 {{ orderDetail.orderInfoVo.returnDateTimeStr }}, {{ orderDetail.orderInfoVo.remainTimeStr }}</span>
    </div>

    <!-- 新增取消时间显示 -->
    <div v-if="orderDetail.orderInfoVo.cancelTimeStr" class="info-item cancel-time">
      <span class="label cancel-label">取消:</span>
      <span class="value cancel-value">{{ orderDetail.orderInfoVo.cancelTimeStr }}</span>
    </div>

    <!-- 其他订单信息 -->
    <div class="info-item">
      <span class="label">订单号:</span>
      <span class="value">{{ orderDetail.orderInfoVo.orderId }}</span>
    </div>
  </div>
</template>
```

#### 2.2 样式规范

```css:样式定义
.cancel-time {
  color: #ff4d4f; /* 取消状态使用红色主题 */
}

.cancel-label {
  color: #ff4d4f;
  font-weight: 500;
}

.cancel-value {
  color: #666666;
  font-size: 14px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

.label {
  color: #333333;
  font-weight: normal;
}

.value {
  color: #666666;
  text-align: right;
}
```

### 3. API接口规范

#### 3.1 现有接口扩展

使用现有的订单详情接口 `/api/order/v1/detail`，无需新增接口。

#### 3.2 请求参数

```javascript
// 请求参数保持不变
{
  orderId: "3792163"
}
```

#### 3.3 响应参数扩展

在现有响应数据结构基础上，`orderInfoVo` 对象新增 `cancelTime` 字段：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "orderInfoVo": {
      "orderId": "3792163",
      "orderStatus": 8,
      "orderStatusStr": "已取消",
      "orderTime": "2025-07-07T12:00:00Z",
      "orderTimeStr": "2025-07-07 12:00",
      "pickupDate": "2025-07-07T15:00:00Z",
      "returnDate": "2025-07-11T15:00:00Z",
      "cancelTime": "2025-07-07T16:30:00Z",  // 新增字段
      "cancelTimeStr": "07-07 16:30",        // 前端格式化后的字段
      // ... 其他现有字段
    }
    // ... 其他现有数据结构
  }
}
```

## UI设计规范

### 1. 布局设计

参考提供的UI设计图，取消时间字段位于订单基本信息区域：

```
订单管理
├── 下单: 2025-07-07 12:00
├── 用车: 2025-07-07 15:00 至 2025-07-11 15:00, 4天
├── 取消: 2025-07-07 16:30  ← 新增字段
├── 订单号: 3792163
└── 渠道订单号: 112814041807428
```

### 2. 视觉规范

- **文本颜色**: #666666 (与现有次要信息保持一致)
- **字体大小**: 14px
- **字体粗细**: normal
- **行高**: 1.5
- **间距**: 上下间距8px

### 3. 状态标识

- 取消时间仅在订单状态为"已取消"时显示
- 显示红色"取消"状态标签，与UI设计图保持一致

## 测试用例

### 1. 功能测试

| 测试场景 | 预期结果 |
|---------|---------|
| 已取消订单且有取消时间 | 正确显示取消时间 |
| 已取消订单但无取消时间 | 不显示取消时间字段 |
| 非取消状态订单 | 不显示取消时间字段 |
| 时间格式显示 | 格式为"MM-dd HH:mm" |

### 2. 兼容性测试

| 测试环境 | 测试内容 |
|---------|---------|
| iOS Safari | 显示效果和交互正常 |
| Android Chrome | 显示效果和交互正常 |
| 微信浏览器 | 显示效果和交互正常 |

### 3. 性能测试

| 测试指标 | 预期值 |
|---------|--------|
| 页面加载时间 | 无明显增加 |
| 数据渲染时间 | < 100ms |

## 上线计划

### 1. 开发阶段

- **第一阶段**: 数据模型和接口变更 (1天)
- **第二阶段**: 前端组件开发和样式调整 (1天)
- **第三阶段**: 测试和优化 (1天)

### 2. 发布计划

- **测试环境**: 2025-08-01
- **预发布环境**: 2025-08-02
- **生产环境**: 2025-08-05

## 风险评估

### 1. 技术风险

- **低风险**: 功能简单，仅为显示字段新增
- **兼容性**: 需要确保不同浏览器显示一致

### 2. 业务风险

- **低风险**: 不影响现有功能，仅为信息展示增强

### 3. 应急预案

- 如出现显示异常，可通过配置开关快速回滚
- 保持现有功能不受影响

## 验收标准

### 1. 功能验收

- ✅ 已取消订单正确显示取消时间
- ✅ 非取消订单不显示取消时间字段
- ✅ 时间格式符合设计要求
- ✅ 样式与现有设计保持一致

### 2. 性能验收

- ✅ 页面加载性能无明显影响
- ✅ 各浏览器兼容性良好

### 3. 用户体验验收

- ✅ 信息展示清晰易懂
- ✅ 符合用户使用习惯

---

**备注**: 本PRD基于擎路SaaS系统现有架构设计，确保新功能与现有系统的无缝集成。开发过程中请严格遵循现有代码规范和设计标准。
