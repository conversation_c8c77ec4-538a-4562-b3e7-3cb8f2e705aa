<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>擎路-订单详情（新增取消时间字段）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.4;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background-color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: #fff;
            padding: 16px;
            border-bottom: 1px solid #eee;
        }
        
        .order-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .status-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .order-type {
            font-size: 14px;
            color: #666;
        }
        
        .status-badge {
            background: #ff4d4f;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .cancel-policy-btn {
            background: #f0f0f0;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
        
        .order-info {
            list-style: none;
        }
        
        .order-info li {
            margin-bottom: 8px;
            font-size: 14px;
            color: #333;
        }
        
        .order-info .creator {
            color: #666;
            margin-left: 8px;
        }
        
        .order-number {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .copy-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 10px;
        }
        
        /* 新增取消时间字段样式 - 高亮显示 */
        .cancel-time-item {
            background: #fff7e6;
            border: 2px solid #ffa940;
            border-radius: 6px;
            padding: 8px;
            margin: 8px 0;
            position: relative;
        }
        
        .cancel-time-item::before {
            content: "NEW";
            position: absolute;
            top: -8px;
            right: 8px;
            background: #ff4d4f;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .cancel-time-text {
            color: #d4380d;
            font-weight: 500;
        }
        
        .tags {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }
        
        .tag {
            background: #f0f0f0;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
        
        .section {
            background: #fff;
            margin-top: 8px;
            padding: 16px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .amount-info {
            text-align: right;
        }
        
        .amount-main {
            font-size: 16px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .amount-detail {
            font-size: 14px;
            color: #666;
        }
        
        .detail-btn {
            background: #f0f0f0;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
        
        .deposit-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .deposit-amounts {
            text-align: right;
        }
        
        .deposit-item {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .records-section {
            background: #fff;
            margin-top: 8px;
            padding: 16px;
        }
        
        .record-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .record-item:last-child {
            border-bottom: none;
        }
        
        .record-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .record-title {
            font-size: 14px;
            color: #333;
        }
        
        .record-count {
            font-size: 12px;
            color: #666;
        }
        
        .record-arrow {
            color: #ccc;
        }
        
        .tenant-section {
            background: #fff;
            margin-top: 8px;
            padding: 16px;
        }
        
        .tenant-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .input-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .tenant-info {
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 8px 16px;
            font-size: 14px;
        }
        
        .info-label {
            color: #666;
        }
        
        .info-value {
            color: #333;
        }
        
        .vehicle-section {
            background: #fff;
            margin-top: 8px;
            padding: 16px;
        }
        
        .vehicle-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
        }
        
        .vehicle-subtitle {
            font-size: 12px;
            color: #666;
            margin-bottom: 16px;
        }
        
        .time-location-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .time-location-item {
            font-size: 12px;
        }
        
        .time-location-label {
            color: #666;
            margin-bottom: 4px;
        }
        
        .time-location-value {
            color: #333;
            margin-bottom: 8px;
        }
        
        .location-detail {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .location-name {
            color: #333;
        }
        
        .location-type {
            color: #666;
        }
        
        .map-link {
            color: #1890ff;
            text-decoration: none;
            font-size: 12px;
        }
        
        .services-section {
            background: #fff;
            margin-top: 8px;
            padding: 16px;
        }
        
        .service-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 12px;
        }
        
        .service-table th,
        .service-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            font-size: 12px;
        }
        
        .service-table th {
            background: #fafafa;
            color: #666;
            font-weight: 500;
        }
        
        .service-name {
            color: #333;
        }
        
        .service-status {
            color: #ff4d4f;
        }
        
        .service-price {
            color: #333;
            text-align: right;
        }
        
        .detail-link {
            color: #1890ff;
            text-decoration: none;
            font-size: 12px;
        }
        
        .footer {
            background: #fff;
            padding: 16px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .more-btn {
            background: #f0f0f0;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            color: #666;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        /* 变更注释样式 */
        .change-annotation {
            background: #fff2e8;
            border-left: 4px solid #ffa940;
            padding: 8px 12px;
            margin: 8px 0;
            font-size: 12px;
            color: #d4380d;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 订单头部信息 -->
        <div class="header">
            <div class="order-status">
                <div class="status-left">
                    <span class="order-type">线下</span>
                    <span class="status-badge">已取消</span>
                </div>
                <button class="cancel-policy-btn">取消政策</button>
            </div>
            
            <ul class="order-info">
                <li>
                    下单：2025-05-19 11:30
                    <span class="creator">创建人：sjz-admin</span>
                </li>
                <li>用车：2025-05-19 12:00 至 2025-05-20 12:00, 1天</li>
                
                <!-- 新增取消时间字段 - 高亮显示 -->
                <li class="cancel-time-item">
                    <span class="cancel-time-text">取消：2025-05-19 11:45</span>
                </li>
                
                <li class="order-number">
                    订单号：1705023
                    <button class="copy-btn">复制</button>
                </li>
            </ul>
            
            <div class="tags">
                <span class="tag">押金未收</span>
                <span class="tag">手续费</span>
                <span class="tag">儿童座椅</span>
            </div>
        </div>
        
        <!-- 变更说明注释 -->
        <div class="change-annotation">
            📝 变更说明：在订单基本信息区域新增"取消时间"字段，显示订单被取消的具体时间，格式为"取消：YYYY-MM-DD HH:mm"，仅在订单状态为"已取消"时显示。
        </div>
        
        <!-- 订单金额 -->
        <div class="section">
            <div class="section-title">
                订单金额
                <div class="amount-info">
                    <div class="amount-main">应收合计8.00元</div>
                    <div class="amount-detail">实收合计8.00元</div>
                </div>
            </div>
            <button class="detail-btn">费用明细</button>
        </div>
        
        <!-- 押金信息 -->
        <div class="section">
            <div class="deposit-info">
                <div class="section-title">押金</div>
                <div class="deposit-amounts">
                    <div class="deposit-item">租车押金3000元</div>
                    <div class="deposit-item">违章押金2000元</div>
                </div>
            </div>
            <button class="detail-btn">押金政策</button>
        </div>
        
        <!-- 记录区域 -->
        <div class="records-section">
            <div class="record-item">
                <div class="record-left">
                    <span class="record-title">续租记录</span>
                    <span class="record-count">3笔</span>
                </div>
                <span class="record-arrow">></span>
            </div>
            <div class="record-item">
                <div class="record-left">
                    <span class="record-title">取还车记录</span>
                    <span class="record-count">取还车信息及租车单据</span>
                </div>
                <span class="record-arrow">></span>
            </div>
            <div class="record-item">
                <div class="record-left">
                    <span class="record-title">车损记录</span>
                    <span class="record-count">0笔</span>
                </div>
                <span class="record-arrow">></span>
            </div>
            <div class="record-item">
                <div class="record-left">
                    <span class="record-title">违章记录</span>
                    <span class="record-count">0笔</span>
                </div>
                <span class="record-arrow">></span>
            </div>
            <div class="record-item">
                <div class="record-left">
                    <span class="record-title">订单备注</span>
                    <span class="record-count">0条</span>
                </div>
                <span class="record-arrow">></span>
            </div>
        </div>
        
        <!-- 承租人信息 -->
        <div class="tenant-section">
            <div class="tenant-header">
                <span class="section-title">承租人信息</span>
                <button class="input-btn">录入证件信息</button>
            </div>
            <div class="tenant-info">
                <span class="info-label">姓名</span>
                <span class="info-value">sjz-admin</span>
                <span class="info-label">证件号</span>
                <span class="info-value">身份证 1231321313213</span>
                <span class="info-label">手机号</span>
                <span class="info-value">13627003150</span>
            </div>
        </div>
        
        <!-- 车辆信息 -->
        <div class="vehicle-section">
            <div class="vehicle-title">舒适型 1376-大众 迈腾GTE插电混动 2020款 GTE 豪华型 琼BF09400</div>
            <div class="vehicle-subtitle">用户预定车型 1376-大众 迈腾GTE插电混动 2020款 GTE 豪华型 有天窗 普牌</div>
            
            <div class="time-location-grid">
                <div class="time-location-item">
                    <div class="time-location-label">预计取车</div>
                    <div class="time-location-value">2025-05-19 12:00</div>
                    <div class="time-location-label">取车门店</div>
                    <div class="location-detail">
                        <span class="location-name">神风租车22</span>
                        <span class="location-type">到店取车</span>
                    </div>
                    <div class="time-location-label">取车地址</div>
                    <div class="location-detail">
                        <span class="location-name">云南普洱梅子湖公园公园分店</span>
                        <a href="#" class="map-link">查看地图</a>
                    </div>
                </div>
                
                <div class="time-location-item">
                    <div class="time-location-label">预计还车</div>
                    <div class="time-location-value">2025-05-20 12:00</div>
                    <div class="time-location-label">还车门店</div>
                    <div class="location-detail">
                        <span class="location-name">神风租车22</span>
                        <span class="location-type">到店还车</span>
                    </div>
                    <div class="time-location-label">还车地址</div>
                    <div class="location-detail">
                        <span class="location-name">云南普洱梅子湖公园公园分店</span>
                        <a href="#" class="map-link">查看地图</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 保险服务 -->
        <div class="services-section">
            <div class="section-title">保险</div>
            <table class="service-table">
                <thead>
                    <tr>
                        <th>服务名称</th>
                        <th>状态</th>
                        <th>总价</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <span class="service-name">基本保障服务费</span>
                            <a href="#" class="detail-link">详情</a>
                        </td>
                        <td class="service-status">部分退款</td>
                        <td class="service-price">￥1.00</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="section-title" style="margin-top: 16px;">附加服务</div>
            <table class="service-table">
                <thead>
                    <tr>
                        <th>服务名称</th>
                        <th>状态</th>
                        <th>总价</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="service-name">手续费</td>
                        <td class="service-status">部分退款</td>
                        <td class="service-price">￥1.00</td>
                    </tr>
                    <tr>
                        <td class="service-name">儿童座椅</td>
                        <td class="service-status">部分退款</td>
                        <td class="service-price">￥5.00</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 底部操作栏 -->
        <div class="footer">
            <button class="more-btn">更多</button>
        </div>
    </div>
</body>
</html>
