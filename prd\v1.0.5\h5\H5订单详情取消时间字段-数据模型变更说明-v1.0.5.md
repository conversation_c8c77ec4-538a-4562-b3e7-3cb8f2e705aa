# H5订单详情取消时间字段 - 数据模型变更说明 v1.0.5

## 文档信息

- **项目**: 擎路 SaaS 管理系统
- **功能**: H5订单详情页面新增取消时间字段
- **版本**: v1.0.5
- **平台**: H5 移动端
- **创建时间**: 2025-07-30
- **文档类型**: 数据模型变更说明

## 变更概述

本次变更主要涉及H5订单详情相关的数据模型，新增 `cancelTime` 字段的处理逻辑，确保在订单状态为"已取消"时能够正确显示取消时间信息。

## 数据模型变更详情

### 1. OrderDetailInfoModel 变更

**文件路径**: `modules/h5/comp-lib/src/services/models/OrderDetailModels/OrderDetailInfoModel.js`

#### 1.1 新增字段

```javascript
// 新增取消时间格式化字段
cancelTimeStr = {
  type: String,
  field: (data) => {
    // 仅在订单状态为已取消(8)且存在取消时间时显示
    if (data.cancelTime && data.orderStatus === 8) {
      return Tempo.format(data.cancelTime, 'MM-dd HH:mm')
    }
    return null
  }
}
```

#### 1.2 变更说明

- **字段名称**: `cancelTimeStr`
- **字段类型**: String
- **显示条件**: 订单状态为8(已取消)且存在cancelTime数据
- **格式化规则**: 使用Tempo工具类，格式为"MM-dd HH:mm"
- **空值处理**: 不满足条件时返回null

### 2. OrderBaseInfoModel 变更

**文件路径**: `modules/h5/comp-lib/src/services/models/OrderDetailModels/OrderBaseInfoModel.js`

#### 2.1 新增字段

```javascript
// 取消时间字符串字段
cancelTimeStr = {
  type: String,
  field: (data) => {
    if (data.cancelTime && data.orderStatus === 8) {
      return Tempo.format(data.cancelTime, 'MM-dd HH:mm')
    }
    return ''
  }
}

// 取消日期字符串字段（备用）
cancelDateStr = {
  type: String,
  field: (data) => {
    if (data.cancelTime && data.orderStatus === 8) {
      return Tempo.format(data.cancelTime)
    }
    return ''
  }
}
```

#### 2.2 变更说明

- **主要字段**: `cancelTimeStr` - 用于页面显示的格式化时间
- **辅助字段**: `cancelDateStr` - 仅显示日期的备用字段
- **一致性**: 与现有时间字段处理逻辑保持一致
- **空值处理**: 基础模型返回空字符串而非null

### 3. 订单状态枚举确认

**文件路径**: `modules/h5/comp-lib/src/constants/index.js`

#### 3.1 现有状态映射

```javascript
export function getOrderStatusStr(value) {
  switch (value) {
    case 0: return '未提交'
    case 1: return '已提交'
    case 2: return '确认中'
    case 3: return '已确认'
    case 4: return '已排车'
    case 5: return '已取车'
    case 6: return '已还车'
    case 7: return '取消中'
    case 8: return '已取消'  // 目标状态
    default: return ''
  }
}
```

#### 3.2 确认信息

- **取消状态值**: 8
- **状态文本**: "已取消"
- **显示条件**: orderStatus === 8

## 时间格式化工具

### 1. Tempo 工具类使用

**文件路径**: `modules/h5/comp-lib/src/utils/index.js`

#### 1.1 格式化方法

```javascript
// 标准时间格式化
Tempo.format(data.cancelTime, 'MM-dd HH:mm')

// 完整日期格式化
Tempo.format(data.cancelTime)

// 可用的格式模式
Tempo.PATTERNS = {
  date: 'YYYY-MM-DD',
  datetime: 'YYYY-MM-DD HH:mm:ss',
  time: 'HH:mm'
}
```

#### 1.2 使用示例

```javascript
// 输入: "2025-07-30T15:30:00Z"
// 输出: "07-30 15:30"
const formattedTime = Tempo.format(cancelTime, 'MM-dd HH:mm')
```

## 数据流转说明

### 1. 数据来源

```
后端API (/api/order/v1/detail)
    ↓
原始数据 (cancelTime: "2025-07-30T15:30:00Z")
    ↓
OrderDetailModel 数据转换
    ↓
格式化字段 (cancelTimeStr: "07-30 15:30")
    ↓
前端组件显示
```

### 2. 条件判断逻辑

```javascript
// 显示条件判断流程
if (data.cancelTime && data.orderStatus === 8) {
  // 1. 检查是否存在取消时间数据
  // 2. 检查订单状态是否为已取消
  // 3. 满足条件则格式化显示
  return Tempo.format(data.cancelTime, 'MM-dd HH:mm')
} else {
  // 不满足条件则不显示
  return null // 或 ''
}
```

## 兼容性说明

### 1. 向后兼容

- **现有字段**: 不影响任何现有字段和功能
- **现有逻辑**: 不修改任何现有的数据处理逻辑
- **现有接口**: 复用现有订单详情接口，无需新增

### 2. 数据兼容

- **历史数据**: 对于没有cancelTime字段的历史订单，字段返回null/空字符串
- **新数据**: 新的取消订单将包含完整的cancelTime信息
- **异常处理**: 数据异常时优雅降级，不影响页面正常显示

### 3. 版本兼容

- **前端版本**: 新版本前端可以处理有无cancelTime的数据
- **后端版本**: 后端需要在订单取消时记录cancelTime字段
- **渐进升级**: 支持前后端独立升级

## 测试数据示例

### 1. 正常取消订单数据

```json
{
  "orderInfoVo": {
    "orderId": "3792163",
    "orderStatus": 8,
    "orderStatusStr": "已取消",
    "orderTime": "2025-07-07T12:00:00Z",
    "cancelTime": "2025-07-07T16:30:00Z",
    "pickupDate": "2025-07-07T15:00:00Z",
    "returnDate": "2025-07-11T15:00:00Z"
  }
}
```

**预期输出**: `cancelTimeStr: "07-07 16:30"`

### 2. 无取消时间的历史数据

```json
{
  "orderInfoVo": {
    "orderId": "3792163",
    "orderStatus": 8,
    "orderStatusStr": "已取消",
    "orderTime": "2025-07-07T12:00:00Z",
    "cancelTime": null,
    "pickupDate": "2025-07-07T15:00:00Z",
    "returnDate": "2025-07-11T15:00:00Z"
  }
}
```

**预期输出**: `cancelTimeStr: null`

### 3. 非取消状态订单

```json
{
  "orderInfoVo": {
    "orderId": "3792164",
    "orderStatus": 5,
    "orderStatusStr": "已取车",
    "orderTime": "2025-07-07T12:00:00Z",
    "cancelTime": null,
    "pickupDate": "2025-07-07T15:00:00Z",
    "returnDate": "2025-07-11T15:00:00Z"
  }
}
```

**预期输出**: `cancelTimeStr: null`

## 实施注意事项

### 1. 开发顺序

1. **后端**: 确保cancelTime字段在订单取消时正确记录
2. **数据模型**: 更新前端数据模型，新增格式化字段
3. **组件**: 更新UI组件，添加取消时间显示逻辑
4. **测试**: 验证各种数据情况下的显示效果

### 2. 代码规范

- 遵循现有的数据模型定义规范
- 使用统一的时间格式化工具
- 保持与现有字段命名风格一致
- 添加必要的注释说明

### 3. 错误处理

- 数据为空时的优雅处理
- 时间格式异常时的容错机制
- 确保不影响页面其他功能的正常运行

---

**备注**: 本变更说明基于擎路SaaS系统现有数据模型架构，确保新增字段与现有系统的完美集成。开发时请严格按照现有代码规范实施。
