# H5订单详情取消时间字段 - 数据模型变更说明 v1.0.5

## 📋 文档信息

| 项目 | 内容 |
|------|------|
| **文档标题** | H5订单详情取消时间字段 - 数据模型变更说明 |
| **版本号** | v1.0.5 |
| **平台** | H5移动端 |
| **创建日期** | 2025-07-30 |
| **文档类型** | 数据模型变更说明 |

## 🎯 变更概述

### 核心变更
在H5订单详情页面新增`cancelTime`字段显示，为已取消订单提供取消时间信息。

### 变更范围
- **OrderEntity** 实体模型扩展
- **OrderDetailInfoModel** 数据模型扩展  
- **API接口** 数据结构调整
- **前端组件** 数据绑定更新

## 🔧 数据模型变更详情

### 1. OrderEntity 实体模型变更

#### 1.1 文件路径
`modules/h5/comp-lib/src/services/entities/OrderEntity.js`

#### 1.2 变更内容
```javascript
export class OrderEntity extends DataX {
  constructor(data = {}) {
    super()
    
    // 现有字段保持不变
    this.id = data.id
    this.orderNo = data.orderNo
    this.status = data.status
    this.startTime = data.startTime
    this.endTime = data.endTime
    this.createTime = data.createTime
    this.updateTime = data.updateTime
    
    // 🆕 新增：取消时间字段
    this.cancelTime = data.cancelTime || null
  }
  
  // 🆕 新增：判断是否为已取消订单
  isCancelled() {
    return this.status === 'CANCELLED'
  }
  
  // 🆕 新增：获取取消时间显示文本
  getCancelTimeText() {
    if (!this.isCancelled() || !this.cancelTime) {
      return ''
    }
    return `取消：${this.formatTime(this.cancelTime)}`
  }
  
  // 现有方法保持不变
  getStatusText() {
    const statusMap = {
      'CONFIRMED': '已确认',
      'DISPATCHED': '已排车', 
      'PICKED_UP': '已取车',
      'RETURNED': '已还车',
      'CANCELLED': '已取消'
    }
    return statusMap[this.status] || '未知状态'
  }
  
  formatTime(time) {
    if (!time) return ''
    return new Date(time).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}
```

#### 1.3 字段说明
| 字段名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| `cancelTime` | String/Date | 订单取消时间戳 | null |

#### 1.4 新增方法说明
| 方法名 | 返回类型 | 说明 |
|--------|----------|------|
| `isCancelled()` | Boolean | 判断订单是否已取消 |
| `getCancelTimeText()` | String | 获取格式化的取消时间显示文本 |

### 2. OrderDetailInfoModel 数据模型变更

#### 2.1 文件路径
`modules/h5/comp-lib/src/services/models/OrderDetailModels/OrderDetailInfoModel.js`

#### 2.2 变更内容
```javascript
import { OrderBaseInfoModel } from './OrderBaseInfoModel'
import { Tempo } from '../../../utils/tempo'

export class OrderDetailInfoModel extends OrderBaseInfoModel {
  constructor(data = {}) {
    super(data)
    
    // 现有字段保持不变
    this.orderTimeStr = this.formatTime(data.orderTime)
    this.remainTimeStr = this.formatRemainTime(data.remainTime)
    
    // 🆕 新增：取消时间相关字段
    this.cancelTime = data.cancelTime || null
    this.cancelTimeStr = this.formatCancelTime(data.cancelTime)
    this.cancelTimeRelative = this.getRelativeCancelTime(data.cancelTime)
  }
  
  // 🆕 新增：格式化取消时间方法
  formatCancelTime(cancelTime) {
    if (!cancelTime) return ''
    return Tempo.format(cancelTime, 'YYYY-MM-DD HH:mm')
  }
  
  // 🆕 新增：获取相对取消时间
  getRelativeCancelTime(cancelTime) {
    if (!cancelTime) return ''
    return Tempo.getRelativeCancelTime(cancelTime)
  }
  
  // 🆕 新增：判断是否显示取消时间
  shouldShowCancelTime() {
    return this.status === 'CANCELLED' && this.cancelTime
  }
  
  // 🆕 新增：获取取消时间显示文本
  getCancelTimeDisplay() {
    if (!this.shouldShowCancelTime()) return ''
    return `取消：${this.cancelTimeStr}`
  }
  
  // 现有方法保持不变
  formatTime(time) {
    if (!time) return ''
    return Tempo.format(time, 'YYYY-MM-DD HH:mm')
  }
  
  formatRemainTime(time) {
    // 现有逻辑保持不变
  }
}
```

#### 2.3 新增字段说明
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `cancelTime` | String/Date | 原始取消时间戳 | "2025-05-19T11:45:00Z" |
| `cancelTimeStr` | String | 格式化的取消时间字符串 | "2025-05-19 11:45" |
| `cancelTimeRelative` | String | 相对时间描述 | "2小时前取消" |

#### 2.4 新增方法说明
| 方法名 | 返回类型 | 说明 |
|--------|----------|------|
| `formatCancelTime(cancelTime)` | String | 格式化取消时间为标准格式 |
| `getRelativeCancelTime(cancelTime)` | String | 获取相对时间描述 |
| `shouldShowCancelTime()` | Boolean | 判断是否应该显示取消时间 |
| `getCancelTimeDisplay()` | String | 获取完整的取消时间显示文本 |

### 3. API接口数据结构变更

#### 3.1 订单详情接口
**接口路径**: `/api/h5/order/detail`

#### 3.2 请求参数
```json
{
  "orderId": "1705023"
}
```

#### 3.3 响应数据结构变更
```json
{
  "code": 200,
  "message": "success", 
  "data": {
    "id": 1705023,
    "orderNo": "1705023",
    "status": "CANCELLED",
    "orderTime": "2025-05-19T11:30:00Z",
    "startTime": "2025-05-19T12:00:00Z",
    "endTime": "2025-05-20T12:00:00Z",
    "createTime": "2025-05-19T11:30:00Z",
    "updateTime": "2025-05-19T11:45:00Z",
    
    // 🆕 新增字段
    "cancelTime": "2025-05-19T11:45:00Z",
    
    // 其他现有字段保持不变
    "vehicleInfo": {
      "id": 1376,
      "brand": "大众",
      "model": "迈腾GTE插电混动",
      "year": "2020款",
      "trim": "GTE 豪华型",
      "plateNo": "琼BF09400"
    },
    "customerInfo": {
      "name": "admin",
      "phone": "13627003150",
      "idCard": "1231321313213"
    },
    "priceInfo": {
      "totalAmount": 8.00,
      "paidAmount": 8.00,
      "deposit": 5000.00
    }
  }
}
```

#### 3.4 新增字段规范
| 字段名 | 类型 | 必填 | 说明 | 格式 |
|--------|------|------|------|------|
| `cancelTime` | String | 否 | 订单取消时间 | ISO 8601格式 |

#### 3.5 字段约束
- **数据类型**: ISO 8601 时间戳字符串
- **时区**: UTC时区，前端负责转换为本地时区
- **空值处理**: 非取消订单该字段为null或不返回
- **格式示例**: "2025-05-19T11:45:00Z"

### 4. 工具函数扩展

#### 4.1 Tempo时间工具扩展
**文件路径**: `modules/h5/comp-lib/src/utils/tempo.js`

```javascript
export class Tempo {
  // 现有方法保持不变
  static format(time, pattern = 'YYYY-MM-DD HH:mm:ss') {
    // 现有实现
  }
  
  // 🆕 新增：专门用于取消时间格式化
  static formatCancelTime(cancelTime) {
    if (!cancelTime) return ''
    
    try {
      const date = new Date(cancelTime)
      if (isNaN(date.getTime())) {
        console.warn('Invalid cancel time:', cancelTime)
        return ''
      }
      
      return this.format(cancelTime, 'YYYY-MM-DD HH:mm')
    } catch (error) {
      console.error('Error formatting cancel time:', error)
      return ''
    }
  }
  
  // 🆕 新增：获取相对时间描述
  static getRelativeCancelTime(cancelTime) {
    if (!cancelTime) return ''
    
    const now = new Date()
    const cancel = new Date(cancelTime)
    const diffMs = now.getTime() - cancel.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMinutes / 60)
    const diffDays = Math.floor(diffHours / 24)
    
    if (diffDays > 0) {
      return `${diffDays}天前取消`
    } else if (diffHours > 0) {
      return `${diffHours}小时前取消`
    } else if (diffMinutes > 0) {
      return `${diffMinutes}分钟前取消`
    } else {
      return '刚刚取消'
    }
  }
  
  // 🆕 新增：验证时间格式
  static isValidTime(time) {
    if (!time) return false
    const date = new Date(time)
    return !isNaN(date.getTime())
  }
}
```

## 🔄 数据流转图

```mermaid
graph TD
    A[订单取消操作] --> B[更新订单状态为CANCELLED]
    B --> C[记录cancelTime时间戳]
    C --> D[保存到数据库]
    D --> E[API返回包含cancelTime的订单数据]
    E --> F[前端OrderEntity解析数据]
    F --> G[OrderDetailInfoModel格式化时间]
    G --> H[Vue组件渲染取消时间]
```

## 📊 影响范围分析

### 5.1 前端影响
- **组件层**: OrderDetail.vue 需要新增取消时间显示逻辑
- **模型层**: OrderEntity 和 OrderDetailInfoModel 需要扩展
- **工具层**: Tempo 工具类需要新增方法
- **类型层**: TypeScript 类型定义需要更新

### 5.2 后端影响
- **数据库**: orders 表需要新增 cancel_time 字段
- **API**: 订单详情接口需要返回 cancelTime 字段
- **业务逻辑**: 订单取消流程需要记录取消时间

### 5.3 测试影响
- **单元测试**: 新增模型和工具函数的测试用例
- **集成测试**: 订单详情页面的端到端测试
- **API测试**: 订单详情接口的数据结构测试

## ⚠️ 注意事项

### 6.1 数据一致性
- 确保订单取消时正确记录 cancelTime
- 处理历史数据中 cancelTime 为空的情况
- 保证时区转换的准确性

### 6.2 性能考虑
- 新增字段对查询性能影响极小
- 时间格式化操作已优化，避免重复计算
- 添加必要的数据库索引

### 6.3 兼容性保证
- 向后兼容，不影响现有功能
- 优雅降级，在数据缺失时隐藏字段
- API版本兼容性处理
