// 擎路组件库主题变量 - 基于qinglu-h5项目的完整CSS变量定义

// 主色调
@primary-color: #1989fa;
@success-color: #07c160;
@warning-color: #ff976a;
@danger-color: #ee0a24;

// 中性色
@white: #fff;
@black: #000;
@gray-1: #f7f8fa;
@gray-2: #f2f3f5;
@gray-3: #ebedf0;
@gray-4: #dcdee0;
@gray-5: #c8c9cc;
@gray-6: #969799;
@gray-7: #646566;
@gray-8: #323233;

// 文字颜色
@text-color: @gray-8;
@text-color-2: @gray-6;
@text-color-3: @gray-5;

// 背景色
@background-color: @gray-1;
@background-color-light: #fafafa;

// 边框
@border-color: @gray-3;
@border-width-base: 1px;
@border-radius-sm: 2px;
@border-radius-md: 4px;
@border-radius-lg: 8px;

// 字体大小
@font-size-xs: 10px;
@font-size-sm: 12px;
@font-size-md: 14px;
@font-size-lg: 16px;
@font-size-xl: 18px;

// 间距
@padding-base: 4px;
@padding-xs: @padding-base * 2;
@padding-sm: @padding-base * 3;
@padding-md: @padding-base * 4;
@padding-lg: @padding-base * 6;
@padding-xl: @padding-base * 8;

// 动画时间
@animation-duration-base: 0.3s;
@animation-duration-fast: 0.2s;

// 阴影
@box-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
@box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.12);

// 擎路品牌色
@qinglu-primary: #00b96b;
@qinglu-secondary: #52c41a;
@qinglu-accent: #1890ff;

// 滑动组件相关变量
@face-background-color-primary-light: @white;

// 完整的CSS变量定义 - 基于qinglu-h5项目
:root {
  // Color Palette
  --van-black: #000000;
  --van-white: #FFFFFF;
  --van-gray-1: #F6F7F9;
  --van-gray-2: #f2f3f5;
  --van-gray-3: #EDEEF3;
  --van-gray-4: #dcdee0;
  --van-gray-5: #CCCCCC;
  --van-gray-6: #999999;
  --van-gray-7: #666666;
  --van-gray-8: #333333;

  --face-red-light: #FFEAEE;
  --face-red-dark: #FF325C;
  --van-red: #FF325C;

  --face-blue-light: #ECF2FD;
  --face-blue-dark: #4286F3;
  --van-blue: #4286F3;

  --van-orange-light: #fffbe8;
  --van-orange-dark: #FFAE5D;
  --van-orange: #FFAE5D;

  --face-green-light: #EAFBF1;
  --face-green-dark: #32D77B;
  --van-green: #32D77B;

  // Gradient Colors
  --van-gradient-red: linear-gradient(to right, #ff6034, #ee0a24);
  --van-gradient-orange: linear-gradient(to right, #ffd01e, #ff8917);
  --face-gradient-blue: linear-gradient(to bottom, #3875C6 0%, #4371CE 100%);

  // Component Colors
  --van-primary-color: var(--van-blue);
  --van-success-color: var(--van-green);
  --van-danger-color: var(--van-red);
  --van-warning-color: var(--van-orange);

  --van-text-color: var(--van-gray-8);
  --van-text-color-2: var(--van-gray-6);
  --van-text-color-3: var(--van-gray-5);
  --van-text-color-white: var(--van-white);
  --van-text-link-color: #576b95;
  --face-text-color-minor: var(--van-gray-7);

  --van-active-color: var(--van-gray-2);
  --van-active-opacity: 0.64;
  --van-disabled-opacity: 0.5;

  // Padding
  --van-padding-base: 4px;
  --van-padding-xs: 8px;
  --van-padding-sm: 12px;
  --van-padding-md: 16px;
  --van-padding-lg: 24px;
  --van-padding-xl: 32px;

  // Margin
  --face-margin-base: 4px;
  --face-margin-xs: 8px;
  --face-margin-sm: 12px;
  --face-margin-md: 16px;
  --face-margin-lg: 24px;
  --face-margin-xl: 32px;

  // Font Size
  --van-font-size-xs: 10px;
  --van-font-size-sm: 12px;
  --van-font-size-md: 14px;
  --van-font-size-lg: 16px;
  --face-font-size-xl: 18px;
  --face-font-size-xxl: 22px;

  // Font Weight
  --face-font-weight-normal: 400;
  --van-font-weight-bold: 500;
  --van-font-weight-bolder: 600;
  --face-font-weight-max: 800;

  // Line Height
  --van-line-height-xs: 14px;
  --van-line-height-sm: 18px;
  --van-line-height-md: 20px;
  --van-line-height-lg: 22px;

  // Border Color
  --van-border-color: var(--van-gray-3);
  --face-border-color-primary: var(--face-blue-dark);
  --face-border-color-gray-5: var(--van-gray-5);

  // Border Width
  --van-border-width-base: 1px;

  // Border Radius
  --van-border-radius-sm: 2px;
  --van-border-radius-md: 4px;
  --van-border-radius-lg: 6px;
  --face-border-radius-xl: 8px;
  --face-border-radius-xxl: 12px;
  --van-border-radius-max: 999px;

  // Box Shadow
  --face-box-shadow-base: 0 3px 8px -3px var(--van-gray-6);
  --face-box-shadow-primary: 0 0 8px -3px var(--face-blue-dark);
  --van-dropdown-menu-box-shadow: 0 2px 12px rgba(100, 101, 102, 0.12);

  // Background
  --van-background-color: var(--van-gray-1);
  --van-background-color-light: var(--van-white);
  --face-background-color-primary: var(--face-blue-dark);
  --face-background-color-primary-light: var(--face-blue-light);
  --face-background-color-gray-3: var(--van-gray-3);
  --face-background-color-warning: var(--van-orange-dark);
  --face-background-color-danger: var(--face-red-dark);

  // 滑动组件相关变量
  --van-slider-active-background-color: var(--van-primary-color);
  --van-slider-inactive-background-color: var(--van-gray-3);
  --van-slider-button-border-radius: 50%;
}
