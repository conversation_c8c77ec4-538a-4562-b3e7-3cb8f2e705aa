---
layout: home
hero:
  name: 擎路H5组件库
  text: 轻量、可靠的移动端Vue组件库
  tagline: 专为擎路业务场景设计的组件库文档
  image:
    alt: 擎路H5组件库
  actions:
    - theme: brand
      text: 开始使用
      link: /components/form/select-field

features:
  - icon: 📱
    title: 表单组件 (12个)
    details: SelectField、DatetimeField、RadioField、UploadField、CalendarField、TagsField等表单相关组件
    link: /components/form/select-field
  - icon: 🏢
    title: 业务组件 (14个)
    details: OrderContract、ArrangeDrivers、VehicleAppearance、TaskDate等业务场景组件
    link: /components/business/order-contract
  - icon: 🔧
    title: 通用组件 (10个)
    details: CalendarPicker、MediaPreview、NavigateBar、IntervalProgress等通用功能组件
    link: /components/common/calendar-picker
---

<div class="vant-home-content">
  <div class="home-content-wrapper">
    <div class="features-section">
      <h2>特性</h2>
      <ul class="feature-list">
        <li>🚀 性能极佳，组件平均体积小，加载速度快</li>
        <li>🎨 36个高质量组件，覆盖移动端主流场景</li>
        <li>💪 基于Vue 3和Vant，技术栈现代化</li>
        <li>📖 提供丰富的中文文档和组件示例</li>
        <li>🍭 支持按需引入和Tree Shaking</li>
        <li>💡 支持TypeScript，提供完整的类型定义</li>
        <li>📱 专为移动端优化，响应式设计</li>
        <li>⚡ 支持Vite构建工具</li>
        <li>🎯 专为擎路业务场景设计</li>
      </ul>
    </div>
  </div>
</div>


