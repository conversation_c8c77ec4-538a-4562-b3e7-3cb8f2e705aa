# Services介绍

## Apis

远程接口集合

*NOTE*: 包括了远程接口调用和Model格式化处理工作。

## Entities

统一处理数据的调用和格式化

*NOTE*: 由于模块的过多添加可能导致逻辑复杂度提升，该模块非必要可以考虑不添加。可以考虑将Model的转换逻辑放到API接口层完成。

## Models

Model作为服务端数据返回后的第一层处理，主要作用是格式化数据和扩展数据。

*NOTE*: 该模块的添加是为了让视图业务层（View层）尽可能的只做渲染工作和业务逻辑处理，将后台返回的数据和前端需要的展示数据都在这一层中处理完成。

### Model的格式化能力

Model的格式化能力是指无论后台返回什么样的数据，视图业务层（View层）都能在不做判断的前提下完成页面的正常渲染，减少视图业务层（View层）过多的判断字段是否存在的问题 **（尤其对数组和对象的判断）**。

这种处理方式是为了尽可能地减少视图业务层（View层）对非业务型的数据结构上的判断，以达到视图业务层（View层）仅关注业务本身的目的。
