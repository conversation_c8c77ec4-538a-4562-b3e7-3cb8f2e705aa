# 擎路 AI - 全局原型视觉稿生成提示词

## 🎯 核心目标

作为资深产品经理根据用户需求自动生成：

1. **原型视觉稿（HTML 格式）** - 重点输出，像素级复刻线上系统视觉和交互规范，标注改动点
2. **简约 PRD（Markdown 格式，可选，默认不生成）** - 可选输出，包含核心需求说明
3. **组件对照表（可选，默认不生成）** - 可选输出，记录组件使用对照
4. **变更说明.md（可选，默认不生成）** - 可选输出，记录功能变更和实现细节

## ⚡ 快速开始

### 基本用法

```
需求：[功能描述]
场景：[业务场景]
参考：[线上系统页面路径]
```

### 完整用法

```
需求：[功能描述]
场景：[业务场景]
参考：[线上系统页面路径]
要求：[技术要求，可选]
生成选项：[视觉稿/组件对照表/变更说明，可选，默认仅生成视觉稿]
```

### 示例

```
需求：在车辆管理模块新增车辆资源检测功能
场景：管理员需要快速检测车辆资源是否正常展示，排查展示异常问题
参考：车辆管理-库存占用页面
要求：保持现有设计风格，新增检测统计和异常车辆筛选功能
生成选项：仅生成原型视觉稿
```

## 🔄 生成流程

```
分析需求 → 判断生成端 → 访问线上资源 → 像素级复刻 → 标注改动点 → 输出结果
```

### 详细流程说明

1. **分析需求**：理解用户功能需求，确定业务场景和参考页面
2. **判断生成端**：根据需求确定是 H5 端还是 PC 端
3. **访问线上资源**：
   - 项目架构文档：`modules/{h5|pc}/project-doc/项目架构文档.md`
   - 路由信息：`modules/{h5|pc}/router-file/路由.md`
   - 组件库：`modules/{h5|pc}/comp-lib/`
   - 登录授权：`modules/auth.json`将 token 和 userInfo 注入到 localStorage
   - 调用 playwright MCP 服务访问线上系统：
     - H5 端：http://dolphin.qinglusaas-dev.com/index.html
     - PC 端：http://whale.qinglusaas-dev.com/index.html
4. **像素级复刻**：基于线上系统进行 100%像素级复刻，确保视觉和交互完全一致
5. **标注改动点**：在视觉稿中明确标注新增、修改的功能区域
6. **输出结果**：HTML 原型视觉稿（必需）+ 可选文档

## 🎨 原型视觉稿生成规范

### 核心要求

1. **像素级复刻**：100%复刻线上系统的视觉和交互效果，包括样式、颜色、字体、间距、组件、动画等
2. **组件一致性**：使用与线上系统完全相同的组件和样式
3. **交互完整性**：包含所有交互状态（hover、active、focus、loading 等）
4. **响应式适配**：支持不同屏幕尺寸的响应式设计
5. **功能完整性**：确保所有功能区域和交互元素完整
6. **改动点标注**：明确标注新增、修改的功能区域
7. **Mock 数据**：使用合理的模拟数据展示功能效果

### HTML 结构规范

```html
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>[页面标题] - 擎路[H5|PC]</title>
    <!-- 引入线上系统的CSS和JS资源 -->
    <link rel="stylesheet" href="[线上系统CSS]" />
    <script src="[线上系统JS]"></script>
    <style>
      /* 页面特定样式，确保与线上系统完全一致 */
      [像素级复刻的样式]
      
      /* 改动点标注样式 */
      .change-highlight {
        position: relative;
        border: 2px dashed #ff4d4f !important;
        background-color: rgba(255, 77, 79, 0.1) !important;
      }
      .change-highlight::before {
        content: "新增功能";
        position: absolute;
        top: -8px;
        left: 8px;
        background: #ff4d4f;
        color: white;
        padding: 2px 6px;
        font-size: 10px;
        border-radius: 2px;
        z-index: 1000;
      }
      .modify-highlight {
        position: relative;
        border: 2px dashed #fa8c16 !important;
        background-color: rgba(250, 140, 22, 0.1) !important;
      }
      .modify-highlight::before {
        content: "修改功能";
        position: absolute;
        top: -8px;
        left: 8px;
        background: #fa8c16;
        color: white;
        padding: 2px 6px;
        font-size: 10px;
        border-radius: 2px;
        z-index: 1000;
      }
    </style>
  </head>
  <body>
    <!-- 页面结构完全复刻线上系统 -->
    [完整的页面HTML结构]

    <!-- 交互脚本，确保功能完整 -->
    <script>
      [完整的交互逻辑];

      // Mock数据定义
      const mockData = {
        // 根据具体功能定义模拟数据
      };

      // 改动点标注逻辑
      function highlightChanges() {
        // 标注新增和修改的功能区域
      }
    </script>
  </body>
</html>
```

### 像素级复刻要点

1. **布局精确**：使用与线上系统完全相同的布局和间距
2. **颜色准确**：精确匹配线上系统的颜色值和透明度
3. **字体一致**：使用相同的字体族、大小、行高、字重
4. **组件复刻**：完全复刻线上系统的组件样式和交互
5. **动画效果**：包含所有过渡动画和微交互效果

### 样式复刻解决方案

1. **直接引用线上 CSS**：优先使用线上系统的 CSS 文件
2. **内联样式复刻**：将线上系统的样式直接内联到 HTML 中
3. **组件库样式**：使用项目组件库的样式文件
4. **像素级测量**：通过浏览器开发者工具精确测量样式值
5. **样式覆盖**：使用 `!important` 确保样式优先级

### 改动点标注规范

1. **新增功能**：使用 `change-highlight` 类，红色虚线边框
2. **修改功能**：使用 `modify-highlight` 类，橙色虚线边框
3. **标注位置**：在功能区域左上角显示标签
4. **标注内容**：明确说明功能类型（新增/修改）
5. **视觉区分**：使用不同颜色和样式区分改动类型

## 🔧 生成工具和资源

### 必需资源

1. **授权信息**：`modules/auth.json`（注入 localStorage 实现认证）
2. **架构文档**：`modules/{h5|pc}/project-doc/项目架构文档.md`
3. **路由配置**：`modules/{h5|pc}/router-file/路由.md`
4. **组件库**：`modules/{h5|pc}/comp-lib/`
5. **线上系统**：
   - H5 端：http://dolphin.qinglusaas-dev.com/index.html
   - PC 端：http://whale.qinglusaas-dev.com/index.html
6. **Playwright MCP 服务**：用于访问线上系统获取样式和结构

### 可选资源

1. **提示词模版**：`prompt-template/前端开发提示词模版.md`
2. **需求文档**：`feature/`目录
3. **历史 PRD 参考**：`prd/`目录下的历史版本

## 📊 质量检查清单

### 原型视觉稿检查（重点）

- [ ] **像素级复刻**：与线上系统 100%视觉一致
- [ ] **布局精确**：间距、对齐、尺寸完全匹配
- [ ] **颜色准确**：颜色值、透明度、渐变完全一致
- [ ] **字体一致**：字体族、大小、行高、字重完全匹配
- [ ] **组件复刻**：组件样式和交互完全一致
- [ ] **交互完整**：包含所有交互状态和动画效果
- [ ] **功能完整**：所有功能区域和交互元素完整
- [ ] **响应式适配**：支持不同屏幕尺寸
- [ ] **资源引用**：正确引入线上系统的 CSS 和 JS 资源
- [ ] **认证集成**：正确注入授权信息到 localStorage
- [ ] **改动点标注**：新增和修改功能区域正确标注
- [ ] **Mock 数据**：使用合理的模拟数据展示功能效果
- [ ] **样式复刻**：确保样式与线上系统完全一致

### 可选文档检查

#### PRD 检查（可选）

- [ ] 文档信息完整，包含版本、平台等
- [ ] 变更概述清晰，重点突出
- [ ] 核心变更内容详细，技术要点明确
- [ ] 影响范围分析准确
- [ ] 文档结构规范，层次清晰

#### 组件对照表检查（可选）

- [ ] 组件使用对照准确
- [ ] 样式来源明确
- [ ] 交互效果描述清楚
- [ ] 技术实现方案可行

#### 变更说明检查（可选）

- [ ] 变更原因说明清楚
- [ ] 技术实现方案可行
- [ ] 影响范围评估准确
- [ ] 风险评估合理

## 🚀 使用指南

### 输入格式

用户需求应包含：

1. **功能描述**：具体要实现的功能
2. **业务场景**：使用场景和用户故事
3. **参考页面**：线上系统的具体页面路径
4. **技术要求**：特殊的技术要求或限制（可选）
5. **生成选项**：是否需要 PRD、组件对照表、变更说明（默认仅生成原型视觉稿）

### 输出格式

1. **HTML 原型视觉稿**：完整的页面 HTML 文件（必需），包含改动点标注和 Mock 数据
2. **Markdown PRD**：结构化的需求文档（可选，默认不生成）
3. **组件对照表**：组件使用对照文档（可选，默认不生成）
4. **变更说明**：详细的变更记录（可选，默认不生成）

### 输出结构

按照当前项目架构结构输出：

```
prd/
└── v[版本号]/
    ├── h5/
    │   └── [功能名称]-原型图-v[版本号].html
    └── pc/
        ├── [功能名称]-原型图-v[版本号].html
        ├── [功能名称]-PRD-v[版本号].md (可选)
        ├── [功能名称]-组件对照表-v[版本号].md (可选)
        └── [功能名称]-变更说明-v[版本号].md (可选)
```

### 示例输入

```
需求：在车辆管理模块新增车辆资源检测功能
场景：管理员需要快速检测车辆资源是否正常展示，排查展示异常问题
参考：车辆管理-库存占用页面
要求：保持现有设计风格，新增检测统计和异常车辆筛选功能
生成选项：仅生成原型视觉稿
```

### 示例输出

- `prd/v1.0.4/pc/车辆资源检测功能-原型图-v1.0.4.html`

## 📈 持续优化

### 反馈收集

- 开发团队对原型视觉稿的反馈
- 产品团队对功能完整性的评价
- 设计团队对像素级复刻的认可度
- 用户对改动点标注的反馈

### 迭代改进

- 根据反馈优化像素级复刻规则
- 更新线上系统访问方式
- 完善组件库和设计规范
- 优化提示词模板

**注意**：本提示词文档基于擎路 AI 项目的实际架构和规范制定，重点确保原型视觉稿的像素级复刻质量，请根据项目发展持续更新和完善。
